spring:
  application:
    name: ais-ma
  profiles:
    active: ${PROFILE:dev}
  cloud:
    nacos:
      config:
        enabled: ${NACOS_ENABLED:true} # 本地自测启动可以使用local配置,关闭配置服务依赖
        namespace: ${NACOS_NAMESPACE:a69dd065-e232-4a94-aa70-52df1373a751}
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:}
        prefix: ${spring.application.name}
        file-extension: yaml
        server-addr: ${NACOS_ADDR:127.0.0.1:31751}
        group: DEFAULT_GROUP
        # 共享配置集数组
        shared-configs:
          - data-id: telecom-ai-common.yaml
            group: DEFAULT_GROUP
        refreshEnabled: true
        import-check: false

jasypt:
  encryptor:
    password: ${JASYPT_PASSWORD:}
