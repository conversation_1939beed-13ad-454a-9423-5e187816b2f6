package com.ai.ais.ma.base.infra.repository;


import com.ai.ais.ma.base.dto.BaseCodeDTO;
import com.ai.ais.ma.base.infra.BaseCodeEntity;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月09日
 */
public interface BaseByCodeRepository<DTO extends BaseCodeDTO, PO extends BaseCodeEntity> extends BaseRepository<DTO, PO> {

    DTO saveByCode(DTO entity);

    boolean removeByCode(String code);

    boolean removeByCodes(Collection<String> codes);

    DTO selectByCode(String code);

    List<DTO> listByCodes(Collection<String> codes);

    boolean updateByCode(DTO entity);


}
