package com.ai.ais.ma.convert.vo;

import com.ai.ais.ma.infra.po.KsKnowledgeReportPO;
import com.chinatelecom.gs.engine.kms.sdk.vo.base.BaseNameVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.common.CodeName;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.chunk.ChunkCompareInfo;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.chunk.ChunkDataVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.publish.KnowledgeProd;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.report.KnowledgeBaseChose;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.SearchParam;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月05日
 */
@Mapper
public interface CommonConverter {

    CommonConverter INSTANCE = Mappers.getMapper(CommonConverter.class);


    List<BaseNameVO> convertReport(List<KsKnowledgeReportPO> reportPO);

    List<CodeName> convertCatalogData(List<KnowledgeProd.CatalogData> catalogData);

    ChunkCompareInfo convert(ChunkDataVO chunkDataVO);

    List<SearchParam.KnowledgeFilter> convertSearchFilter(List<KnowledgeBaseChose> knowledgeBaseChose);

}
