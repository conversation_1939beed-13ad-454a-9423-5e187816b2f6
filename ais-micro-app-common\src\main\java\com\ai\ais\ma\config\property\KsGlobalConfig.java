package com.ai.ais.ma.config.property;


import com.ai.ais.ma.enums.CacheType;
import com.chinatelecom.gs.engine.kms.sdk.enums.KnowledgeType;
import com.chinatelecom.gs.engine.kms.sdk.enums.ResponseType;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import com.google.common.collect.ImmutableMap;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年11月29日
 */

@Getter
@Setter
@Component
@RefreshScope
@ConfigurationProperties(prefix = "ks")
@Slf4j
public class KsGlobalConfig {

    /**
     * 系统配置
     */
    private SystemConfig system = new SystemConfig();


    /**
     * 缓存配置
     */
    private CacheConfig cache = new CacheConfig();

    /**
     * 线程池配置
     */
    private PoolConfig pool = new PoolConfig();

    /**
     * 外部系统地址配置
     */
    private RpcConfig rpc = new RpcConfig();

    /**
     * 配置与关联的权限过滤
     */
    private Map<String, FilterModeConfig> filterModes = new HashMap<>();

    /**
     * 某个用户定制的AI报告配置
     */
    private Map<String, List<AiReportCustomConfig>> aiReportConfig = new HashMap<>();

    private RagConfig rag = new RagConfig();

    private MaBizConfig maBizConfig = new MaBizConfig();
    /**
     * 知识类型与模型的映射
     */



    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SystemConfig {
        // 部署环境: dev、test、prod
        private String env = "prod";
        // 是否打印拦截日志
        private Boolean logEnabled = true;

        private String rootPath = "/usr/src/app/telecom";

        /**
         * 自定义应用名称
         */
        private String customAppName;
        /**
         * 自定义logo
         */
        private String customLogo;
        /**
         * 自定义icon
         */
        private String customIcon;

        /**
         * 来源系统的地址配置
         */
        private Map<AppSourceType, String> sourceUrlType = new HashMap<>();

        private String excludeWebFilters = "/web/platform/swap/session/info";

    }


    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CacheConfig {
        // 缓存类型
        private CacheType type = CacheType.NONE;

    }

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ThreadPoolConfig {
        private int coreSize = 8;
        private int maxSize = 16;
        private int queueSize = -1;
        private String name;
    }

    @Getter
    @Setter
    public static class PoolConfig {
        private ThreadPoolConfig defaultPool = new ThreadPoolConfig(16, 32, -1, "default-pool");
        private ThreadPoolConfig ssePool = new ThreadPoolConfig(16, 32, -1, "sse-pool");
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RpcConfig {
        /**
         * gsEngine后端服务地址
         */
        private String gsUrl = "http://telecom-ai-gs-engine-srv:8092/";

    }


    @Data
    public static class FilterModeConfig {
        private String name;
        private String description;
        private Set<String> filteredCodes;
    }

    @Data
    public static class AiReportCustomConfig {

        private Set<String> keyword;

        private String headLine;

        private String outline;

        private String content;

    }

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RagConfig {
        private boolean enableMulti;
        private boolean enableQueryRewrite = true;
        private boolean enableImageModel = true;
        private boolean modelUseRewrite = true;
        private boolean emptyToResp;
        private boolean retryWithOriginalQuery = true;
        private boolean enableRank;
        private int recallSize = 20;
        private int reqSize = 8;
        private int refMaxLength = 40;
        private Map<ResponseType, String> responseText;
        private boolean enableLog;
        private String referencePattern;
        private String referencePrefix;
        private String referenceFormat;
        private int multiRounds;
        private int multiReferenceSize;
        private int bufferLength = 10;
        private Duration timeout = Duration.ofSeconds(60);
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MaBizConfig {


        // 文件名称长度限制
        private int fileNameMaxLength = 512;
        // 文件大小限制  默认100M
        private long fileSizeMaxLength = 200;
        /**
         * 使用单独线程池解析大文件的文件大小下限（单位M）
         */
        private Map<KnowledgeType, Long> largeFileSplit = ImmutableMap.of(
                KnowledgeType.IMAGE, 2L,
                KnowledgeType.TEXT, 5L,
                KnowledgeType.EXCEL, 5L,
                KnowledgeType.FAQ, 5L,
                KnowledgeType.PDF, 10L,
                KnowledgeType.WORD, 10L,
                KnowledgeType.PPT, 10L,
                KnowledgeType.VIDEO, 30L,
                KnowledgeType.AUDIO, 30L
        );
        /**
         * 导读开关，默认关闭
         */
        private Boolean summarySwitch = false;

        /**
         * 默认初始化copy数据的应用编码
         */
        private String defaultInitAppCode;
        /**
         * 默认初始化copy数据的知识库编码
         */
        private String defaultInitKnowledgeBase;

        /**
         * 智能报告 faq相似问 等使用的模型编码，如果不配置，使用默认大模型
         */
        private String fastLlmModelCode;

        /**
         * faq抽取最大分片大小
         */
        private int faqExtractMaxChunkSize = 30;

        /**
         * 推送搜索分片每次推送的最大条数
         */
        private Integer pushSearchMaxBatchSize = 50;

        int tagMaxLevel = 5;

        /**
         * 搜索召回全量数据每页查询的最大条数
         */
        private Integer searchMaxBatchPageSize = 10000;
        /**
         * 搜索召回全量数据每页查询的最大页数
         */
        private Integer searchMaxBatchPageNum = 100;


    }
}
