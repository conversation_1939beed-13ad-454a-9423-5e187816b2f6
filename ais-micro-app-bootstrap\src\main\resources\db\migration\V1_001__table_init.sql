CREATE TABLE `ks_knowledge_report` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(64) NOT NULL COMMENT '名称',
  `code` varchar(64) NOT NULL COMMENT 'Code',
  `app_code` varchar(64) NOT NULL COMMENT '应用Code',
  `theme_text` varchar(512) NOT NULL COMMENT '用户原始主题',
  `content` mediumtext COMMENT '文章内容',
  `type` varchar(64) NOT NULL COMMENT '文章内容格式',
  `outline_data` text COMMENT '大纲结构',
  `chose` varchar(10000) DEFAULT NULL COMMENT '智能问答选择项',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_code` (`code`),
  KEY `idx_app_code` (`app_code`,`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='知识报告表';
