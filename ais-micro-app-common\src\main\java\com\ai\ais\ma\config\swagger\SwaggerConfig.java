package com.ai.ais.ma.config.swagger;


import com.chinatelelcom.gs.engine.sdk.common.constants.HeaderKeys;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.ReflectionUtils;
import org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.builders.RequestParameterBuilder;
import springfox.documentation.oas.annotations.EnableOpenApi;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.ParameterType;
import springfox.documentation.service.RequestParameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.spring.web.plugins.WebFluxRequestHandlerProvider;
import springfox.documentation.spring.web.plugins.WebMvcRequestHandlerProvider;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * SwaggerConfig
 * 传统swagger访问地址： http://localhost:8092/ais/swagger-ui/index.html
 * knife4j风格地址： http://localhost:8092/ais/doc.html
 *
 * <AUTHOR>
 * @date 2022-10-15 20:32
 */
@Configuration
@EnableOpenApi
public class SwaggerConfig {

    @Value("${springfox.documentation.enabled:false}")
    private Boolean springfoxEnabled;

    /**
     * 处理spring2.7和swagger3.0不兼容的问题
     * Springfox 假设 Spring MVC 的路径匹配策略是 ant-path-matcher，而 Spring Boot 2.7.x版本的默认匹配策略是 path-pattern-matcher
     *
     * @return
     */
    @Bean
    public static BeanPostProcessor springfoxHandlerProviderBeanPostProcessor() {
        return new BeanPostProcessor() {
            @Override
            public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
                if (bean instanceof WebMvcRequestHandlerProvider || bean instanceof WebFluxRequestHandlerProvider) {
                    customizeSpringfoxHandlerMappings(getHandlerMappings(bean));
                }
                return bean;
            }

            private <T extends RequestMappingInfoHandlerMapping> void customizeSpringfoxHandlerMappings(List<T> mappings) {
                List<T> copy = mappings.stream()
                        .filter(mapping -> mapping.getPatternParser() == null)
                        .collect(Collectors.toList());
                mappings.clear();
                mappings.addAll(copy);
            }

            @SuppressWarnings("unchecked")
            private List<RequestMappingInfoHandlerMapping> getHandlerMappings(Object bean) {
                try {
                    Field field = ReflectionUtils.findField(bean.getClass(), "handlerMappings");
                    if (field == null) {
                        throw new IllegalStateException("Field 'handlerMappings' not found in class " + bean.getClass().getName());
                    }
                    ReflectionUtils.makeAccessible(field);
                    return (List<RequestMappingInfoHandlerMapping>) field.get(bean);
                } catch (IllegalArgumentException | IllegalAccessException e) {
                    throw new IllegalStateException(e);
                }
            }
        };
    }

    @Bean
    public Docket docket() {
        List<RequestParameter> requestHeadParam = getRequestParameters();

        return new Docket(DocumentationType.OAS_30)
                .apiInfo(apiInfo())
                .enable(springfoxEnabled)
                .groupName("系统接口")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.ai.ais.ma"))
                .paths(PathSelectors.any())
                .build()
                .globalRequestParameters(requestHeadParam);
    }

    private List<RequestParameter> getRequestParameters() {
        List<RequestParameter> requestHeadParam = new ArrayList<>();
        RequestParameter appCodeHeader = new RequestParameterBuilder().name(HeaderKeys.APP_CODE).in(ParameterType.HEADER).description("应用信息").required(false).build();
        requestHeadParam.add(appCodeHeader);
        return requestHeadParam;
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("AIS-KS")
                .description("AIS-KS")
                .version("1.0.0")
                .build();
    }
}
