package com.ai.ais.ma.config.interceptor.filter;

import com.ai.ais.ma.common.utils.InterceptorUtils;
import com.ai.ais.ma.config.interceptor.OpenApiContextInterceptor;
import com.ai.ais.ma.config.interceptor.TraceIdInterceptor;
import com.ai.ais.ma.config.interceptor.WebContextInterceptor;
import com.ai.ais.ma.config.property.KsGlobalConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月31日
 */
@Component
@Slf4j
@Order(1)
public class BaseCustomerFilter implements Filter {

    private BaseInterceptorRegistry interceptorRegistry = new BaseInterceptorRegistry();

    private AntPathMatcher antPathMatcher = new AntPathMatcher();

    @Resource
    private KsGlobalConfig ksGlobalConfig;

    @Resource
    private WebContextInterceptor webContextInterceptor;

    @Resource
    private OpenApiContextInterceptor openApiContextInterceptor;

    @Resource
    private TraceIdInterceptor traceIdInterceptor;

    @PostConstruct
    public void init() {
        interceptorRegistry.addInterceptor(traceIdInterceptor).addPathPatterns("/**").order(Integer.MIN_VALUE);
        interceptorRegistry.addInterceptor(webContextInterceptor).addPathPatterns("/**/web/**")
                .excludePathPatterns(ksGlobalConfig.getSystem().getExcludeWebFilters().split(",")).order(2);
        interceptorRegistry.addInterceptor(openApiContextInterceptor).addPathPatterns("/**/openapi/**").order(3);
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        Exception exception = null;
        try {
            boolean preResult = preHandle(request, response);
            if (preResult) {
                chain.doFilter(request, response);
                postHandle(request, response);
            }
        } catch (Exception e) {
            log.error("自定义拦截器执行异常", e);
            exception = e;
            InterceptorUtils.writeError((HttpServletResponse) response, "A0022", "基础拦截器执行异常");
        } finally {
            try {
                afterCompletion(request, response, exception);
            } catch (Exception e) {
                log.error("后置处理异常", e);
            }
        }
    }


    private boolean preHandle(ServletRequest request, ServletResponse response) throws Exception {
        List<BaseInterceptorRegistration> registrations = interceptorRegistry.getRegistrations();
        for (int i = 0; i < registrations.size(); i++) {
            BaseInterceptorRegistration registration = registrations.get(i);
            boolean isFilter = matchingPath(request, registration);
            if (isFilter) {
                boolean interceptor = registration.getInterceptor().preHandle(request, response);
                if (!interceptor) {
                    log.warn("自定义拦截不通过：{}", registration.getInterceptor().getClass().getName());
                    return false;
                }
            }
        }
        return true;
    }

    private boolean matchingPath(ServletRequest request, BaseInterceptorRegistration registration) {
        String servletPath = ((HttpServletRequest) request).getServletPath();
        List<String> excludeUrls = registration.getExcludePatterns();
        if (CollectionUtils.isNotEmpty(excludeUrls)) {
            for (String excludeUrl : excludeUrls) {
                if (antPathMatcher.match(excludeUrl, servletPath)) {
                    return false;
                }
            }
        }

        List<String> includePatterns = registration.getIncludePatterns();
        if (CollectionUtils.isNotEmpty(includePatterns)) {
            for (String includePattern : includePatterns) {
                if (antPathMatcher.match(includePattern, servletPath)) {
                    return true;
                }
            }
        }
        return false;
    }

    private void afterCompletion(ServletRequest request, ServletResponse response, Exception exception) throws Exception {
        List<BaseInterceptorRegistration> registrations = interceptorRegistry.getRegistrations();
        for (int i = 0; i < registrations.size(); i++) {
            BaseInterceptorRegistration registration = registrations.get(i);
            boolean isFilter = matchingPath(request, registration);
            if (isFilter) {
                registration.getInterceptor().afterCompletion(request, response, exception);
            }
        }
    }

    private void postHandle(ServletRequest request, ServletResponse response) throws Exception {
        List<BaseInterceptorRegistration> registrations = interceptorRegistry.getRegistrations();
        for (int i = 0; i < registrations.size(); i++) {
            BaseInterceptorRegistration registration = registrations.get(i);
            boolean isFilter = matchingPath(request, registration);
            if (isFilter) {
                registration.getInterceptor().postHandle(request, response);
            }
        }
    }

}
