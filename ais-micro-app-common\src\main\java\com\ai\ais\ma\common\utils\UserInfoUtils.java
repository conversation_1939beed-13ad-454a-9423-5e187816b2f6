package com.ai.ais.ma.common.utils;

import com.ai.ais.ma.common.context.TeamInfo;
import com.chinatelecom.cloud.common.pojo.BaseResult;
import com.chinatelecom.cloud.platform.client.rpc.UserInfo;
import com.chinatelecom.cloud.platform.client.util.AuthUtils;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月30日
 */
@Slf4j
public class UserInfoUtils {

    private static final Cache<String, List<TeamInfo>> CACHE = Caffeine.newBuilder().expireAfterWrite(10, TimeUnit.SECONDS).maximumSize(1000).build();

    private static final Cache<String, UserInfo> USER_CACHE = Caffeine.newBuilder().expireAfterWrite(60, TimeUnit.SECONDS).maximumSize(1000).build();


    /**
     * 获取用户团队信息
     *
     * @return
     */
    public static List<TeamInfo> getUserTeam() {
        // 暂时不需要团队信息，不进行查询
        return Collections.emptyList();

//        String key = StringUtils.join(RequestContext.getUserId(), "_", RequestContext.getTenantId());
//
//        List<TeamInfo> result = CACHE.getIfPresent(key);
//        if (result == null) {
//            List<TeamInfo> teamList = Lists.newArrayList();
//            try {
//                BaseResult<List<Team>> baseResult = AuthUtils.getTeamByUser(RequestContext.getUserId(), RequestContext.getTenantId());
//                if (baseResult != null && baseResult.ifSuccess()) {
//                    List<Team> teams = baseResult.getData();
//                    if (CollectionUtils.isNotEmpty(teams)) {
//                        teamList.addAll(teams.stream().map(team -> TeamInfo.builder()
//                                .teamCode(team.getTeamCode()).name(team.getName()).build()).collect(Collectors.toList()));
//                        CACHE.put(key, teamList);
//                    }
//                } else {
//                    log.warn("查询用户团队信息失败，result:{}", JsonUtils.toJsonString(baseResult));
//                }
//            } catch (Exception e) {
//                log.error("查询用户团队信息异常", e);
////            throw new BizException(e, "00023", "查询用户团队信息异常");
//            }
//            return teamList;
//        } else {
//            return result;
//        }
    }

    public static UserInfo getUserInfo(String tenantId, String userId) {
        String key = StringUtils.join(userId, "_", tenantId);
        UserInfo result = USER_CACHE.getIfPresent(key);
        if (result == null) {
            BaseResult<UserInfo> baseResult = AuthUtils.getUserById(tenantId, userId);
            if (baseResult != null && baseResult.ifSuccess() && baseResult.getData() != null) {
                result = baseResult.getData();
                USER_CACHE.put(key, result);
            }
        }
        return result;
    }

}
