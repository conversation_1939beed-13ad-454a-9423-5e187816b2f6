package com.ai.ais.ma;


import com.ai.ais.ma.base.infra.BaseAppPO;
import com.ai.ais.ma.base.infra.BaseExtendMapper;
import com.ai.ais.ma.base.service.BaseAppByCodeService;
import com.ai.ais.ma.base.service.impl.BaseAppServiceByCodeImpl;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.builder.CustomFile;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.google.common.collect.ImmutableList;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CodeGeneratorKms {

    static String url = "******************************************************************************************************************************************";
    static String un = "****";
    static String pw = "*****";

    static List<String> tablename = ImmutableList.of("ks_knowledge_report");  //表名
    static String path = "D:\\work\\code\\mybatis";

    public static void main(String[] args) {
        FastAutoGenerator.create(url, un, pw)
                .globalConfig(builder -> {
                    builder.author("mybatis-plus") // 设置作者
                            .enableSpringdoc() // 开启 swagger 模式
                            //.disableOpenDir() // 不允许自动打开输出目录
                            .outputDir(path) // 指定输出目录
                    ;

                })
                //包配置
                .packageConfig(builder -> {
                    builder.parent("com.ai.ais.ma")
                            .entity("infra.po")
                            .service("service")
                            .serviceImpl("service.impl")
                            .mapper("infra.mapper")
                            // .xml("dao.xml")
                            .controller("controller")
                            .build();
                })
                //表策略配置
                .strategyConfig(builder -> {
                    builder.enableSkipView()
                            .disableSqlFilter()
                            .addTablePrefix("biz_", "sys_")
                            .addInclude(tablename)
                            .build();
                })
                //entity策略
                .strategyConfig(builder -> {
                    builder.entityBuilder()
                            //.idType(IdType.ASSIGN_ID)
                            .superClass(BaseAppPO.class)
                            .disableSerialVersionUID()
                            .enableRemoveIsPrefix()
                            .enableLombok()
                            .formatFileName("%s")
                            .addIgnoreColumns("yn", "create_time", "update_time", "code")
                            .disable()
                            .build();
                })
                //controller 策略
                .strategyConfig(builder -> {
                    builder.controllerBuilder()
                            .enableHyphenStyle()
                            .enableRestStyle()
                            .formatFileName("%sController")
                            .build();
                })
                //Service 策略
                .strategyConfig(builder -> {
                    builder.serviceBuilder()
                            .superServiceClass(BaseAppByCodeService.class)
                            .superServiceImplClass(BaseAppServiceByCodeImpl.class)
                            .formatServiceFileName("%sAppService")
                            .formatServiceImplFileName("%sAppServiceImpl")
                            .build();
                })
                //mapper 策略
                .strategyConfig(builder -> {
                    builder.mapperBuilder()
                            .superClass(BaseExtendMapper.class)
                            //.enableBaseResultMap()
                            //.enableBaseColumnList()
                            .disableMapperXml()
                            .formatMapperFileName("%sMapper")
                            .disable()
                            .build();
                })
                //注入自定义配置
                .injectionConfig(builder -> {
                    /**自定义生成模板参数,在ftl模版里取值使用**/
                    Map<String, Object> data = new HashMap<>();
                    data.put("entityBuilderModel", true);
                    data.put("chainModel", false);
                    data.put("swagger", true);
                    data.put("entitySerialVersionUID", true);
                    data.put("entityLombokModel", true);
                    builder.customMap(data);
                    List<CustomFile> customFiles = new ArrayList();
                    /**DTO实体**/
                    CustomFile updateParamFile = new CustomFile.Builder()
                            .fileName("UpdateParam.java")//生成java文件名称，要和ftl模版里的文件名保持一致
                            .templatePath("/templates/UpdateParam.java.ftl")//实体模板位置
                            .packageName("model.vo")//生成文件包名
                            .build();
                    customFiles.add(updateParamFile);
                    CustomFile createParamFile = new CustomFile.Builder()
                            .fileName("CreateParam.java")
                            .templatePath("/templates/CreateParam.java.ftl")
                            .packageName("model.vo")
                            .build();
                    customFiles.add(createParamFile);

                    CustomFile QueryParamFile = new CustomFile.Builder()
                            .fileName("QueryParam.java")
                            .templatePath("/templates/QueryParam.java.ftl")
                            .packageName("model.vo")
                            .build();
                    customFiles.add(QueryParamFile);
                    CustomFile DTOFile = new CustomFile.Builder()
                            .fileName("DTO.java")
                            .templatePath("/templates/DTO.java.ftl")
                            .packageName("model.dto")
                            .build();
                    customFiles.add(DTOFile);
                    CustomFile RepositoryFile = new CustomFile.Builder()
                            .fileName("Repository.java")
                            .templatePath("/templates/Repository.java.ftl")
                            .packageName("repository")
                            .build();
                    customFiles.add(RepositoryFile);
                    CustomFile RepositoryImplFile = new CustomFile.Builder()
                            .fileName("RepositoryImpl.java")
                            .templatePath("/templates/RepositoryImpl.java.ftl")
                            .packageName("repository.impl")
                            .build();
                    customFiles.add(RepositoryImplFile);

                    CustomFile DtoConverterFile = new CustomFile.Builder()
                            .fileName("DtoConverter.java")
                            .templatePath("/templates/DtoConverter.java.ftl")
                            .packageName("convert.dto")
                            .build();
                    customFiles.add(DtoConverterFile);

                    CustomFile VoConverterFile = new CustomFile.Builder()
                            .fileName("VoConverter.java")
                            .templatePath("/templates/VoConverter.java.ftl")
                            .packageName("convert.vo")
                            .build();
                    customFiles.add(VoConverterFile);

                    /**Vo实体**/
                    CustomFile customFileVO = new CustomFile.Builder()
                            .fileName("VO.java")
                            .templatePath("/templates/VO.java.ftl")
                            .packageName("model.vo").build();
                    customFiles.add(customFileVO);

                    CustomFile poFile = new CustomFile.Builder()
                            .fileName("PO.java")
                            .templatePath("/templates/entity.java.ftl")
                            .packageName("infra.po")
                            .build();
                    customFiles.add(poFile);
                    CustomFile mapperFile = new CustomFile.Builder()
                            .fileName("Mapper.java")
                            .templatePath("/templates/mapper.java.ftl")
                            .packageName("infra.mapper")
                            .build();
                    customFiles.add(mapperFile);

                    builder.customFile(customFiles);
                })
                .templateEngine(new FreemarkerTemplateEngine())
                .execute();
    }
}
