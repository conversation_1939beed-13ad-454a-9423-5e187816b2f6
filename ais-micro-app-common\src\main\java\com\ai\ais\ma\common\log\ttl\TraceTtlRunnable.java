package com.ai.ais.ma.common.log.ttl;

import com.ai.ais.ma.common.log.utils.MDCTraceUtils;
import com.alibaba.ttl.TtlRunnable;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class TraceTtlRunnable implements Runnable {

    private final TtlRunnable delegate;

    public static TraceTtlRunnable get(Runnable runnable) {
        return get(runnable, false);
    }

    public static TraceTtlRunnable get(Runnable runnable, boolean releaseTtlValueReferenceAfterRun) {
        if (runnable == null) {
            return null;
        } else if (runnable instanceof TraceTtlRunnable) {
            return (TraceTtlRunnable) runnable;
        } else if (runnable instanceof TtlRunnable) {
            runnable = ((TtlRunnable) runnable).getRunnable();
        }
        Runnable finalRunnable = runnable;
        return new TraceTtlRunnable(TtlRunnable.get(() -> {
            MDCTraceUtils.putTrace(MDCTraceUtils.getTraceId(), MDCTraceUtils.getSpanId());
            finalRunnable.run();
        }, releaseTtlValueReferenceAfterRun));
    }

    @Override
    public void run() {
        delegate.run();
    }
}
