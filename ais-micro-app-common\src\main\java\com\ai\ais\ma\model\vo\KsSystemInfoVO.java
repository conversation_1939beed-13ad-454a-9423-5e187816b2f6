package com.ai.ais.ma.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年04月21日
 */
@Data
public class KsSystemInfoVO {

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "用户名称")
    private String userName;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "授权开始时间")
    private Date licenseStartDate;

    @Schema(description = "授权结束时间")
    private Date licenseExpireDate;

    @Schema(description = "授权天数")
    private Long licenseDays;

    @Schema(description = "产品版本")
    private String version;

    @Schema(description = "打包时间")
    private String timestamp;

    @Schema(description = "提交id")
    private String commitSha;

    @Schema(description = "打包分支")
    private String gitBranch;

    @Schema(description = "打包tag")
    private String gitTag;
}
