package ${package.Service};

import ${package.Entity}.${entity}PO;
import ${superServiceClassPackage};

import ${package.Parent}.base.service.BaseAppByCodeService;
import ${package.Parent}.convert.vo.${entity}VoConverter;
import ${package.Parent}.infra.po.${entity}PO;
import ${package.Parent}.model.dto.${entity}DTO;
import ${package.Parent}.model.vo.${entity}CreateParam;
import ${package.Parent}.model.vo.${entity}QueryParam;
import ${package.Parent}.model.vo.${entity}UpdateParam;
import ${package.Parent}.model.vo.${entity}VO;
import ${package.Parent}.repository.${entity}Repository;

import java.util.List;

/**
* <p>
    * ${table.comment!} 服务类
    * </p>
*
* <AUTHOR>
* @since ${date}
*/
public interface ${table.serviceName} extends ${superServiceClass}<${entity}Repository, ${entity}QueryParam, ${entity}VoConverter,${entity}PO,${entity}DTO,${entity}VO, ${entity}CreateParam, ${entity}UpdateParam> {


}
