package com.ai.ais.ma.convert.vo;

import com.ai.ais.ma.base.converter.BaseVoConverter;
import com.ai.ais.ma.model.dto.KsKnowledgeReportDTO;
import com.ai.ais.ma.model.vo.KsKnowledgeReportCreateParam;
import com.ai.ais.ma.model.vo.KsKnowledgeReportUpdateParam;
import com.ai.ais.ma.model.vo.KsKnowledgeReportVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface KsKnowledgeReportVoConverter extends BaseVoConverter<KsKnowledgeReportDTO, KsKnowledgeReportVO, KsKnowledgeReportCreateParam, KsKnowledgeReportUpdateParam> {

    KsKnowledgeReportVoConverter INSTANCE = Mappers.getMapper(KsKnowledgeReportVoConverter.class);


}


