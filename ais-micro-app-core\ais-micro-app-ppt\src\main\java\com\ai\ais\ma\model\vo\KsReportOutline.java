package com.ai.ais.ma.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * @Author: gaoxianjun
 * @CreateTime: 2024-12-16
 * @Description:
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class KsReportOutline {

    // 当前大纲级别
    @JsonIgnore
    private transient String level;

    /**
     * md格式的级别加标题
     */
    @JsonIgnore
    private transient String levelTitleMd;

    /**
     * 当前大纲生成内容
     */
    private transient String content;

    @Schema(description = "大纲标题")
    private String title;

    @Schema(description = "大纲摘要/引言")
    private String introduction;

    @Schema(description = "大纲当前节点状态")
    private String status;



    @Schema(description = "子级大纲")
    private List<KsReportOutline> children;


}
