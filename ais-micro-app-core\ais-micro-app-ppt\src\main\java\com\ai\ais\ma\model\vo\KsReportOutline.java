package com.ai.ais.ma.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * @Author: g<PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-12-16
 * @Description:
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class KsReportOutline {

    @Schema(description = "大纲标题")
    private String title;

    @Schema(description = "子级大纲")
    private List<KsReportOutline> children;
}
