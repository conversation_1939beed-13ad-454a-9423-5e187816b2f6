package com.ai.ais.ma.base.infra.repository.impl;

import com.ai.ais.ma.base.dto.RootDTO;
import com.ai.ais.ma.base.infra.BaseConvertor;
import com.ai.ais.ma.base.infra.BaseExtendMapper;
import com.ai.ais.ma.base.infra.RootEntity;
import com.ai.ais.ma.base.infra.repository.BaseRepository;
import com.ai.ais.ma.common.utils.IdGenerator;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.ReflectionKit;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * BaseRepositoryImpl
 *
 * <AUTHOR>
 * @date 2023-04-06 16:41
 * <p>
 * <M extends BaseMapper<T>, T>
 */
public abstract class BaseRepositoryImpl<SE extends BaseExtendMapper<PO>, PO extends RootEntity, DTO extends RootDTO> implements BaseRepository<DTO, PO>, BaseConvertor<DTO, PO> {

    @Autowired
    protected SE baseExtendMapper;

    protected Class<DTO> dtoClass = currentDtoClass();

    protected Class<PO> poClass = currentPoClass();

    private Class<DTO> currentDtoClass() {
        return (Class<DTO>) ReflectionKit.getSuperClassGenericType(this.getClass(), BaseRepositoryImpl.class, 2);
    }

    private Class<PO> currentPoClass() {
        return (Class<PO>) ReflectionKit.getSuperClassGenericType(this.getClass(), BaseRepositoryImpl.class, 1);
    }

    @Override
    public Class<DTO> getDTOClass() {
        return dtoClass;
    }

    @Override
    public Class<PO> getPOClass() {
        return poClass;
    }

    /**
     * 获取对应 entity 的 BaseMapper
     *
     * @return BaseMapper
     */
    @Override
    public BaseMapper<PO> getBaseMapper() {
        Assert.notNull(this.baseExtendMapper, "baseMapper can not be null");
        return this.baseExtendMapper;
    }

    @Override
    public IPage<DTO> convertToDtoPages(IPage<DTO> dtoPage, IPage<PO> poPage) {
        dtoPage = dtoPage == null ? new Page<>() : dtoPage;
        List<DTO> dtoList = convertToDto(poPage.getRecords());
        dtoPage.setPages(poPage.getPages());
        dtoPage.setCurrent(poPage.getCurrent());
        dtoPage.setTotal(poPage.getTotal());
        dtoPage.setSize(poPage.getSize());
        dtoPage.setRecords(dtoList);
        return dtoPage;
    }

    @Override
    public IPage<DTO> convertToDtoPages(IPage<PO> poPage) {
        return convertToDtoPages(null, poPage);
    }

    @Override
    public String generateCode(String key) {
        return IdGenerator.getId(key);
    }

    @Override
    public boolean save(DTO dto) {
        PO po = convertToPo(dto);
        boolean result = SqlHelper.retBool(getBaseMapper().insert(po));
        dto.setId(po.getId());
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveBatch(Collection<DTO> dtoList) {
        getBaseMapper().insert(convertToPo(dtoList));
        return true;
    }

    @Override
    public boolean saveBatch(Collection<DTO> dtoList, int batchSize) {
        getBaseMapper().insert(convertToPo(dtoList), batchSize);
        return true;
    }

    @Override
    public boolean saveOrUpdateBatch(Collection<DTO> entityList) {
        getBaseMapper().insertOrUpdate(convertToPo(entityList));
        return true;
    }

    @Override
    public boolean saveOrUpdateBatch(Collection<DTO> entityList, int batchSize) {
        getBaseMapper().insertOrUpdate(convertToPo(entityList), batchSize);
        return true;
    }

    @Override
    public boolean removeById(Serializable id) {
        return SqlHelper.retBool(getBaseMapper().deleteById(id));
    }

    @Override
    public boolean removeById(Serializable id, boolean useFill) {
        return SqlHelper.retBool(getBaseMapper().deleteById(id, useFill));
    }

    @Override
    public boolean removeById(DTO entity) {
        return SqlHelper.retBool(getBaseMapper().deleteById(convertToPo(entity)));
    }

    @Override
    public boolean removeByMap(Map<String, Object> columnMap) {
        return SqlHelper.retBool(getBaseMapper().deleteByMap(columnMap));
    }

    @Override
    public boolean removeByIds(Collection<?> list) {
        return SqlHelper.retBool(getBaseMapper().deleteByIds(list));
    }

    @Override
    public boolean removeByIds(Collection<?> list, boolean useFill) {
        return SqlHelper.retBool(getBaseMapper().deleteByIds(list, useFill));
    }

    @Override
    public boolean removeBatchByIds(Collection<?> list) {
        return SqlHelper.retBool(getBaseMapper().deleteByIds(list));
    }

    @Override
    public boolean remove(Wrapper<PO> queryWrapper) {
        return SqlHelper.retBool(getBaseMapper().delete(queryWrapper));
    }

    @Override
    public boolean updateById(DTO entity) {
        return SqlHelper.retBool(getBaseMapper().updateById(convertToPo(entity)));
    }

    @Override
    public boolean saveOrUpdate(DTO entity) {
        return getBaseMapper().insertOrUpdate(convertToPo(entity));
    }

    @Override
    public DTO getById(Serializable id) {
        return convertToDto(getBaseMapper().selectById(id));
    }

    @Override
    public List<DTO> listByIds(Collection<? extends Serializable> idList) {
        return convertToDto(getBaseMapper().selectBatchIds(idList));
    }

    @Override
    public List<DTO> listByMap(Map<String, Object> columnMap) {
        return convertToDto(getBaseMapper().selectByMap(columnMap));
    }

    @Override
    public <E extends IPage<DTO>> E page(E page) {
        return page(page, Wrappers.emptyWrapper());
    }

    @Override
    public <E extends IPage<DTO>> E page(E page, Wrapper<PO> queryWrapper) {
        IPage<PO> pagePO = new Page<>();
        pagePO.setCurrent(page.getCurrent());
        pagePO.setSize(page.getSize());
        getBaseMapper().selectPage(pagePO, queryWrapper);
        convertToDtoPages(page, pagePO);
        return page;
    }

    /**
     * 查询总记录数
     *
     * @see Wrappers#emptyWrapper()
     */
    @Override
    public long count() {
        return getBaseMapper().selectCount(Wrappers.emptyWrapper());
    }

    /**
     * 根据 Wrapper 条件，查询总记录数
     *
     * @param queryWrapper 实体对象封装操作类 {@link com.baomidou.mybatisplus.core.conditions.query.QueryWrapper}
     */
    @Override
    public long count(Wrapper<PO> queryWrapper) {
        return getBaseMapper().selectCount(queryWrapper);
    }

    @Override
    public DTO selectOne(Wrapper<PO> queryWrapper) {
        return convertToDto(getBaseMapper().selectOne(queryWrapper));
    }

    @Override
    public boolean updateOne(DTO entity, Wrapper<PO> updateWrapper) {
        Assert.notNull(updateWrapper, "updateWrapper can not be null");
        PO po = convertToPo(entity);
        return SqlHelper.retBool(getBaseMapper().update(po, updateWrapper));
    }

    @Override
    public String getOnlyOneSql() {
        return " limit 1 ";
    }

}
