package com.ai.ais.ma.config.cache.config;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/4 16:59
 */
@Slf4j
@Configuration
public class RedissonConfiguration {

    @Autowired
    private RedisProperties redisProperties;

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        if (isCluster()) {
            log.info("init cluster");
            config.useClusterServers().setNodeAddresses(
                    redisProperties.getCluster().getNodes().stream().map(node -> "redis://" + node)
                            .collect(Collectors.toList()));
            if (StringUtils.isNotBlank(redisProperties.getPassword())) {
                config.useClusterServers().setPassword(redisProperties.getPassword());
            }
        } else {
            log.info("init single server");
            config.useSingleServer()
                    .setAddress("redis://" + redisProperties.getHost() + ":" + redisProperties.getPort());
            if (StringUtils.isNotBlank(redisProperties.getPassword())) {
                config.useSingleServer().setPassword(redisProperties.getPassword());
            }
        }
        return Redisson.create(config);
    }

    /**
     * 检查是否集群模式
     */
    private boolean isCluster() {
        return redisProperties.getCluster() != null && CollUtil.isNotEmpty(redisProperties.getCluster().getNodes());
    }
}
