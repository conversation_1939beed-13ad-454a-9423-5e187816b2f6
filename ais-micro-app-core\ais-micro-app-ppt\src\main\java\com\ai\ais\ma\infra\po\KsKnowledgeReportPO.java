package com.ai.ais.ma.infra.po;

import com.ai.ais.ma.base.infra.BaseAppPO;
import com.ai.ais.ma.enums.KsReportType;
import com.ai.ais.ma.model.vo.KsKnwlChose;
import com.ai.ais.ma.model.vo.KsReportOutline;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <p>
 * 知识报告表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
@Getter
@Setter
@TableName(value = "ks_knowledge_report", autoResultMap = true)
public class KsKnowledgeReportPO extends BaseAppPO {

    private static final long serialVersionUID = 1L;

    @TableField("`name`")
    private String name;

    @TableField(value = "`theme_text`")
    private String themeText;

    @TableField("`content`")
    private String content;

    @TableField("`type`")
    private KsReportType type;

    @TableField(value = "`outline_data`", typeHandler = JacksonTypeHandler.class)
    private List<KsReportOutline> outlineData;

    @TableField(value = "`chose`", typeHandler = JacksonTypeHandler.class)
    private KsKnwlChose chose;
}
