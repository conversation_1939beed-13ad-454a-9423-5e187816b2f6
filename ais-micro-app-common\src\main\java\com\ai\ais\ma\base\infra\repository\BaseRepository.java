package com.ai.ais.ma.base.infra.repository;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * BaseRepository
 *
 * <AUTHOR>
 * @date 2023-04-06 15:27
 */
public interface BaseRepository<DTO, PO> {

    IPage<DTO> convertToDtoPages(IPage<DTO> dtoPages, IPage<PO> poPages);

    IPage<DTO> convertToDtoPages(IPage<PO> poPages);

    /**
     * 获取对应 entity 的 BaseMapper
     *
     * @return BaseMapper
     */
    BaseMapper<PO> getBaseMapper();

    /**
     * 生成指定前缀的编码
     *
     * @param key
     * @return
     */
    String generateCode(String key);

    /**
     * 保存一条数据
     *
     * @param entity
     * @return
     */
    boolean save(DTO entity);

    /**
     * 插入（批量）
     *
     * @param entityList 实体对象集合
     */
    boolean saveBatch(Collection<DTO> entityList);

    /**
     * 插入（批量）
     *
     * @param entityList 实体对象集合
     * @param batchSize  插入批次数量
     */
    boolean saveBatch(Collection<DTO> entityList, int batchSize);

    /**
     * 批量修改插入
     *
     * @param entityList 实体对象集合
     */
    boolean saveOrUpdateBatch(Collection<DTO> entityList);

    /**
     * 批量修改插入
     *
     * @param entityList 实体对象集合
     * @param batchSize  每次的数量
     */
    boolean saveOrUpdateBatch(Collection<DTO> entityList, int batchSize);

    /**
     * 根据 ID 删除
     *
     * @param id 主键ID
     */
    boolean removeById(Serializable id);

    /**
     * 根据 ID 删除
     *
     * @param id      主键(类型必须与实体类型字段保持一致)
     * @param useFill 是否启用填充(为true的情况,会将入参转换实体进行delete删除)
     * @return 删除结果
     * @since 3.5.0
     */
    boolean removeById(Serializable id, boolean useFill);

    /**
     * 根据实体(ID)删除
     *
     * @param entity 实体
     * @since 3.4.4
     */
    boolean removeById(DTO entity);

    /**
     * 根据 columnMap 条件，删除记录
     *
     * @param columnMap 表字段 map 对象
     */
    boolean removeByMap(Map<String, Object> columnMap);

    /**
     * 删除（根据ID 批量删除）
     *
     * @param list 主键ID或实体列表
     */
    boolean removeByIds(Collection<?> list);

    /**
     * 批量删除
     *
     * @param list    主键ID或实体列表
     * @param useFill 是否填充(为true的情况,会将入参转换实体进行delete删除)
     * @return 删除结果
     * @since 3.5.0
     */
    boolean removeByIds(Collection<?> list, boolean useFill);

    /**
     * 批量删除(jdbc批量提交)
     *
     * @param list 主键ID或实体列表(主键ID类型必须与实体类型字段保持一致)
     * @return 删除结果
     * @since 3.5.0
     */
    boolean removeBatchByIds(Collection<?> list);

    /**
     * 删除数据
     *
     * @param queryWrapper
     * @return
     */
    boolean remove(Wrapper<PO> queryWrapper);

    /**
     * 根据 ID 选择修改
     *
     * @param entity 实体对象
     */
    boolean updateById(DTO entity);

    /**
     * TableId 注解存在更新记录，否插入一条记录
     *
     * @param entity 实体对象
     */
    boolean saveOrUpdate(DTO entity);

    /**
     * 根据 ID 查询
     *
     * @param id 主键ID
     */
    DTO getById(Serializable id);

    /**
     * 查询（根据ID 批量查询）
     *
     * @param idList 主键ID列表
     */
    List<DTO> listByIds(Collection<? extends Serializable> idList);

    /**
     * 查询（根据 columnMap 条件）
     *
     * @param columnMap 表字段 map 对象
     */
    List<DTO> listByMap(Map<String, Object> columnMap);

    /**
     * 无条件翻页查询
     * 形参:
     * page – 翻页对象
     * 请参阅:
     * Wrappers.emptyWrapper()
     */
    <E extends IPage<DTO>> E page(E page);

    /**
     * 条件翻页查询
     * 形参:
     * page – 翻页对象
     */
    <E extends IPage<DTO>> E page(E page, Wrapper<PO> queryWrapper);


    /**
     * 查询总记录数
     *
     * @see Wrappers#emptyWrapper()
     */
    long count();

    /**
     * 根据 Wrapper 条件，查询总记录数
     *
     * @param queryWrapper 实体对象封装操作类 {@link com.baomidou.mybatisplus.core.conditions.query.QueryWrapper}
     */
    long count(Wrapper<PO> queryWrapper);

    DTO selectOne(Wrapper<PO> queryWrapper);

    boolean updateOne(DTO entity, Wrapper<PO> updateWrapper);

    String getOnlyOneSql();
}
