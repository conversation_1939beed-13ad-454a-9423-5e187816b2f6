package com.ai.ais.ma.config.cache.aspect;


import com.ai.ais.ma.config.cache.aspect.annotation.Lock;
import com.ai.ais.ma.config.cache.lock.DistributedLock;
import com.ai.ais.ma.config.cache.lock.ZLock;
import com.ai.ais.ma.config.cache.lock.exception.LockException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.annotation.Order;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

/**
 * 分布式锁切面
 * 如果锁定的方法有事务注解且事务需要在锁内执行, 需要设置事务的优先级 @EnableTransactionManagement(order = 1000)
 * 两个默认优先级都是 Integer.MAX_VALUE, 不指定事务可能先执行
 */
@Slf4j
@Aspect
@Order(value = 100)
@Component
public class LockAspect {

    @Autowired(required = false)
    private DistributedLock locker;

    /**
     * 用于SpEL表达式解析.
     */
    private SpelExpressionParser spelExpressionParser = new SpelExpressionParser();
    /**
     * 用于获取方法参数定义名字.
     */
    private DefaultParameterNameDiscoverer nameDiscoverer = new DefaultParameterNameDiscoverer();

    @Around("@within(lock) || @annotation(lock)")
    public Object aroundLock(ProceedingJoinPoint point, Lock lock) throws Throwable {
        if (lock == null) {
            // 获取类上的注解
            lock = point.getTarget().getClass().getDeclaredAnnotation(Lock.class);
        }
        String lockKey = lock.key();
        if (locker == null) {
            throw new LockException("DistributedLock is null");
        }
        if (StringUtils.isEmpty(lockKey)) {
            throw new LockException("lockKey is null");
        }

        if (lockKey.contains("#")) {
            MethodSignature methodSignature = (MethodSignature) point.getSignature();
            //获取方法参数值
            Object[] args = point.getArgs();
            lockKey = getValBySpEL(lockKey, methodSignature, args);
        }
        ZLock lockObj = null;
        try {
            //加锁
            if (lock.waitTime() >= 0) {
                lockObj = locker.tryLock(lockKey, lock.waitTime(), lock.leaseTime(), lock.unit(), lock.isFair());
            } else {
                lockObj = locker.lock(lockKey, lock.leaseTime(), lock.unit(), lock.isFair());
            }

            if (lockObj != null) {
                return point.proceed();
            } else {
                throw new LockException("B0002", lock.message());
            }
        } finally {
            locker.unlock(lockObj);
        }
    }

    /**
     * 解析spEL表达式
     */
    private String getValBySpEL(String spEL, MethodSignature methodSignature, Object[] args) {
        //获取方法形参名数组
        String[] paramNames = nameDiscoverer.getParameterNames(methodSignature.getMethod());
        if (paramNames != null && paramNames.length > 0) {
            Expression expression = spelExpressionParser.parseExpression(spEL);
            // spring的表达式上下文对象
            EvaluationContext context = new StandardEvaluationContext();
            // 给上下文赋值
            for (int i = 0; i < args.length; i++) {
                context.setVariable(paramNames[i], args[i]);
            }
            return String.valueOf(expression.getValue(context));
        }
        return "null";
    }
}
