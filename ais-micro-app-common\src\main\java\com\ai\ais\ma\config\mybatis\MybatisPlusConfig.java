package com.ai.ais.ma.config.mybatis;

import com.ai.ais.ma.common.context.RequestContext;
import com.ai.ais.ma.common.context.RequestInfo;
import com.ai.ais.ma.common.utils.JsonUtils;
import com.ai.ais.ma.config.mybatis.handler.UpdateFillHandler;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.BlockAttackInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.Set;


@Slf4j
@Configuration
@MapperScan(basePackages = {
        "com.ai.ais.ma.infra.mapper"
})
public class MybatisPlusConfig {

    private static final Set<String> INTERCEPTOR_APP_CODE_TABLES = Sets.newHashSet(
            "ks_knowledge_report", "`ks_knowledge_report`"
    );


    @PostConstruct
    public void init() {
        JacksonTypeHandler.setObjectMapper(JsonUtils.newInstance());
    }


    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor mybatisPlusInterceptor = new MybatisPlusInterceptor();

        // 更新操作自动填充用户信息
        mybatisPlusInterceptor.addInnerInterceptor(new UpdateFillInterceptor(new UpdateFillHandler() {
        }));

        // 防止全表更新删除操作, 必须在租户插件之前
        mybatisPlusInterceptor.addInnerInterceptor(new BlockAttackInnerInterceptor());
//
        // 这里添加应用自动隔离处理， 使用租户插件， 如果需要拦截，需要手动指定表明，默认不会配置拦截
        mybatisPlusInterceptor.addInnerInterceptor(new TenantLineInnerInterceptor(new TenantLineHandler() {
            @Override
            public String getTenantIdColumn() {
                return "app_code";
            }

            @Override
            public Expression getTenantId() {
                return getTenantIdExpression();
            }

            @Override
            public boolean ignoreTable(String tableName) {
                return !INTERCEPTOR_APP_CODE_TABLES.contains(tableName);
            }
        }));

        // 分页插件
        PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor(DbType.MYSQL);
        // 设置分页上限
        paginationInnerInterceptor.setMaxLimit(5000L);
        // 设置leftJoin 优化
        paginationInnerInterceptor.setOptimizeJoin(true);
        mybatisPlusInterceptor.addInnerInterceptor(paginationInnerInterceptor);

        // 乐观锁插件
        OptimisticLockerInnerInterceptor optimisticLockerInnerInterceptor = new OptimisticLockerInnerInterceptor();
        mybatisPlusInterceptor.addInnerInterceptor(optimisticLockerInnerInterceptor);

        return mybatisPlusInterceptor;
    }

    private Expression getTenantIdExpression() {
        RequestInfo requestInfo = RequestContext.getAndCheck();
        if (requestInfo == null || StringUtils.isBlank(requestInfo.getAppCode())) {
            throw new BizException("B0001", "无法获取到应用信息");
        }
        return new StringValue(requestInfo.getAppCode());
    }

}
