package com.ai.ais.ma.utils.sse;

import com.ai.ais.ma.common.sse.watcher.AbstractSseWatcher;
import com.ai.ais.ma.common.sse.watcher.gs.GsMsgData;
import com.ai.ais.ma.common.utils.JsonUtils;
import com.ai.ais.ma.model.vo.KsReportOutline;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.sse.EventSource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * 大纲生成流式处理器
 * 负责处理大纲生成的SSE流式响应，实时解析Markdown并转换为ReportOutline对象
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-18
 */
@Slf4j
public class GsOutlineStreamWatcher extends AbstractSseWatcher {

    @Getter
    @Setter
    protected SseEmitter sseEmitter;

    @Getter
    /**
     * 存储大模型的所有输出
     */
    protected StringBuffer markdownBuffer = new StringBuffer();

    @Getter
    /**
     * 当前解析的大纲结构
     */
    protected List<KsReportOutline> currentOutlines = new ArrayList<>();

    public GsOutlineStreamWatcher(CountDownLatch latch) {
        super(latch);
    }

    @Override
    public void onEvent(EventSource eventSource, String id, String type, String data) {
        try {
            log.debug("接收到大纲生成消息内容：{}", data);
            if (StringUtils.isNotEmpty(data)) {
                // 解析消息内容
                String textContent = extractTextFromData(data);
                if (StringUtils.isNotEmpty(textContent)) {
                    markdownBuffer.append(textContent);
                    
                    // 实时解析Markdown并更新大纲结构
                    List<KsReportOutline> newOutlines = parseMarkdownPPT(markdownBuffer.toString());
                    currentOutlines = newOutlines;
                    
                    // 构建返回给前端的数据
                    OutlineStreamResponse response = OutlineStreamResponse.builder()
                            .type("outline_update")
                            .markdownContent(textContent)
                            .outlines(currentOutlines)
                            .isComplete(false)
                            .build();
                    
                    // 发送给前端
                    if (sseEmitter != null) {
                        SseEmitter.SseEventBuilder sendDataBuilder = SseEmitter.event()
                                .id(id)
                                .name("outline_stream")
                                .data(JsonUtils.toJsonString(response));
                        sseEmitter.send(sendDataBuilder);
                    }
                }
            }
        } catch (IOException e) {
            // 大部分情况是用户主动取消发送,前端关闭连接后,这里会捕获到IO异常
            clientAbort = true;
            throw new BizException(e, "AK001", "用户主动关闭管道");
        } catch (Exception e) {
            clientAbort = true;
            log.error("大纲流式输出异常", e);
            throw new BizException(e, "AK002", "大纲流式输出异常");
        }
    }

    @Override
    public void onClosed(EventSource eventSource) {
        try {
            // 流结束时发送最终完整的大纲结构
            if (sseEmitter != null && !clientAbort) {
                OutlineStreamResponse finalResponse = OutlineStreamResponse.builder()
                        .type("outline_complete")
                        .markdownContent(markdownBuffer.toString())
                        .outlines(currentOutlines)
                        .isComplete(true)
                        .build();
                
                SseEmitter.SseEventBuilder sendDataBuilder = SseEmitter.event()
                        .name("outline_complete")
                        .data(JsonUtils.toJsonString(finalResponse));
                sseEmitter.send(sendDataBuilder);
            }
        } catch (Exception e) {
            log.error("发送最终大纲结构异常", e);
        }
        super.onClosed(eventSource);
    }

    /**
     * 从SSE数据中提取文本内容
     */
    private String extractTextFromData(String data) {
        if (StringUtils.isEmpty(data)) {
            return "";
        }

        try {
            if (data.trim().startsWith("{")) {
                GsMsgData gsMsgData = JsonUtils.parseObject(data, GsMsgData.class);
                if (gsMsgData != null && gsMsgData.getText() != null) {
                    return gsMsgData.getText();
                }
            } else {
                // 如果不是JSON格式，直接返回原始数据
                return data;
            }
        } catch (Exception e) {
            log.debug("解析消息内容失败，使用原始数据: {}", data);
            return data;
        }

        return "";
    }

    /**
     * 解析markdown大纲为ReportOutline结构
     * 参考原有的parseMarkdownPPT方法
     */
    private List<KsReportOutline> parseMarkdownPPT(String markdown) {
        if (markdown == null || markdown.isEmpty()) {
            return new ArrayList<>();
        }

        String[] lines = markdown.split("\n");
        List<KsReportOutline> outline = new ArrayList<>();
        KsReportOutline currentLevel1 = null;
        KsReportOutline currentLevel2 = null;
        KsReportOutline currentLevel3 = null;

        for (String line : lines) {
            line = line.trim();
            if (StringUtils.isBlank(line)) {
                continue;
            }

            if (line.startsWith("# ")) {
                currentLevel1 = KsReportOutline.builder()
                        .title(line.substring(2).trim())
                        .children(new ArrayList<>())
                        .build();
                outline.add(currentLevel1);
                currentLevel2 = null;
                currentLevel3 = null;
            } else if (line.startsWith("## ")) {
                currentLevel2 = KsReportOutline.builder()
                        .title(line.substring(3).trim())
                        .children(new ArrayList<>())
                        .build();
                if (currentLevel1 != null && currentLevel1.getChildren() != null) {
                    currentLevel1.getChildren().add(currentLevel2);
                }
                currentLevel3 = null;
            } else if (line.startsWith("### ")) {
                currentLevel3 = KsReportOutline.builder()
                        .title(line.substring(4).trim())
                        .children(new ArrayList<>())
                        .build();
                if (currentLevel2 != null && currentLevel2.getChildren() != null) {
                    currentLevel2.getChildren().add(currentLevel3);
                }
            } else if (line.startsWith("- ")) {
                if (currentLevel3 != null) {
                    KsReportOutline content = KsReportOutline.builder()
                            .title(line.substring(2).trim())
                            .children(new ArrayList<>())
                            .build();
                    currentLevel3.getChildren().add(content);
                }
            }
        }

        return outline;
    }

    /**
     * 大纲流式响应数据结构
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class OutlineStreamResponse {
        private String type;
        private String markdownContent;
        private List<KsReportOutline> outlines;
        private boolean isComplete;
    }
}
