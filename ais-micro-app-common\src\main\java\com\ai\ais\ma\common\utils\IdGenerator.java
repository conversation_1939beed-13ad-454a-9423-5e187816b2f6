package com.ai.ais.ma.common.utils;

import com.alibaba.cloud.commons.lang.StringUtils;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.UnknownHostException;
import java.text.NumberFormat;

/**
 * IdGenerator ID生成器
 *
 * <AUTHOR>
 * @date 2023-04-11 15:50
 */
public class IdGenerator {

    /**
     * 系统开始时间截 (UTC 2017-06-28 00:00:00)
     */
    private static final long START_TIME = 1498608000000L;
    /**
     * 机器id所占的位数
     */
    private static final long WORKER_ID_BITS = 5L;
    /**
     * 数据标识id所占的位数
     */
    private static final long DATA_CENTER_ID_BITS = 5L;
    /**
     * 支持的最大机器id(十进制)，结果是31 (这个移位算法可以很快的计算出几位二进制数所能表示的最大十进制数)
     * -1L 左移 5位 (worker id 所占位数) 即 5位二进制所能获得的最大十进制数 - 31
     */
    private static final long MAX_WORKER_ID = ~(-1L << WORKER_ID_BITS);
    /**
     * 支持的最大数据标识id - 31
     */
    private static final long MAX_DATA_CENTER_ID = ~(-1L << DATA_CENTER_ID_BITS);
    /**
     * 序列在id中占的位数
     */
    private static final long SEQUENCE_BITS = 12L;
    /**
     * 机器ID 左移位数 - 12 (即末 sequence 所占用的位数)
     */
    private static final long WORKER_ID_MOVE_BITS = SEQUENCE_BITS;
    /**
     * 数据标识id 左移位数 - 17(12+5)
     */
    private static final long DATA_CENTER_ID_MOVE_BITS = SEQUENCE_BITS + WORKER_ID_BITS;
    /**
     * 时间截向 左移位数 - 22(5+5+12)
     */
    private static final long TIMESTAMP_MOVE_BITS = SEQUENCE_BITS + WORKER_ID_BITS + DATA_CENTER_ID_BITS;
    /**
     * 生成序列的掩码(12位所对应的最大整数值)，这里为4095 (0b111111111111=0xfff=4095)
     */
    private static final long SEQUENCE_MASK = ~(-1L << SEQUENCE_BITS);
    /**
     * 工作节点
     */
    private static final long FINAL_WORKER_ID = 1L;
    private static final IdGenerator ID_WORKER = new IdGenerator(getWorkerId(), getDatacenterId());
    /**
     * 工作机器ID(0~31)
     */
    private long workerId;
    /**
     * 数据中心ID(0~31)
     */
    private long dataCenterId;
    /**
     * 毫秒内序列(0~4095)
     */
    private long sequence = 0L;
    /**
     * 上次生成ID的时间截
     */
    private long lastTimestamp = -1L;


    /**
     * 构造函数
     *
     * @param workerId     工作ID (0~31)
     * @param dataCenterId 数据中心ID (0~31)
     */
    private IdGenerator(long workerId, long dataCenterId) {
        if (workerId > MAX_WORKER_ID || workerId < 0) {
            throw new IllegalArgumentException(String.format("Worker Id can't be greater than %d or less than 0", MAX_WORKER_ID));
        }
        if (dataCenterId > MAX_DATA_CENTER_ID || dataCenterId < 0) {
            throw new IllegalArgumentException(String.format("DataCenter Id can't be greater than %d or less than 0", MAX_DATA_CENTER_ID));
        }
        this.workerId = workerId;
        this.dataCenterId = dataCenterId;
    }

    private static long getDatacenterId() {
        long dataCenterId = 0L;
        try {
            InetAddress ip = InetAddress.getLocalHost();
            NetworkInterface network = NetworkInterface.getByInetAddress(ip);
            if (network == null) {
                dataCenterId = 1L;
            } else {
                byte[] mac = network.getHardwareAddress();
                if (null != mac) {
                    dataCenterId = ((0x000000FF & (long) mac[mac.length - 1]) | (0x0000FF00 & (((long) mac[mac.length - 2]) << 8))) >> 6;
                    dataCenterId = dataCenterId % (MAX_DATA_CENTER_ID + 1);
                }
            }
        } catch (Exception e) {
            return -1;
        }
        return dataCenterId;
    }
    // 阻塞到下一个毫秒 即 直到获得新的时间戳

    private static long getWorkerId() {
        return FINAL_WORKER_ID;
    }

    private static String getIp() {
        // 获得后三位IP
        String ip;
        try {
            String localhost = InetAddress.getLocalHost().toString();
            int index = localhost.lastIndexOf('.');
            localhost = localhost.substring(index + 1);
            NumberFormat tempFormat = NumberFormat.getNumberInstance();
            tempFormat.setGroupingUsed(false);
            tempFormat.setMaximumIntegerDigits(3);
            tempFormat.setMinimumIntegerDigits(3);
            ip = tempFormat.format(Integer.parseInt(localhost));
        } catch (UnknownHostException e) {
            // 当发生异常时，IP地址为000
            ip = "000";
        }
        return ip;
    }

    public static String id() {
        return String.valueOf(ID_WORKER.nextId());
    }

    /**
     * 生产knowledge开头的知识id
     *
     * @return msgid
     **/
    public static String getKnowledgeId() {
        return "" + ID_WORKER.nextId();
    }

    /**
     * 生产msg开头的消息id,后三位为ip地址
     *
     * @return msgid
     **/
    public static String getMessageId() {
        return "msg" + ID_WORKER.nextId() + getIp();
    }

    /**
     * 生成file开头的文件序列号id,后三位为ip地址
     *
     * @return String
     */
    public static String getFileSerialNo() {
        return "file" + ID_WORKER.nextId() + getIp();
    }

    /**
     * 生产区别应用且区别类型的自定义id
     *
     * @return 返回appname+idtype+分布式唯一id类型的id
     **/
    public static String getId(String appName, String idType) {
        return appName + idType + ID_WORKER.nextId();
    }

    /**
     * 生产自定义前缀的id,前缀超过6位自动截取前六位
     *
     * @return 返回idtype+分布式唯一id类型的id
     **/
    public static String getId(String perfix) {
        if (StringUtils.isBlank(perfix)) {
            return String.valueOf(ID_WORKER.nextId());
        }
        perfix = perfix.length() > 6 ? perfix.substring(0, 6) : perfix;
        return perfix + ID_WORKER.nextId();
    }

    /**
     * 线程安全的获得下一个 ID 的方法
     *
     * @return long
     * @date 2023/8/3
     */
    private synchronized long nextId() {
        long timestamp = currentTime();
        // 如果当前时间小于上一次ID生成的时间戳: 说明系统时钟回退过 - 这个时候应当抛出异常
        if (timestamp < lastTimestamp) {
            throw new IllegalArgumentException(
                    String.format("Clock moved backwards.  Refusing to generate id for %d milliseconds", lastTimestamp - timestamp));
        }
        // 如果是同一时间生成的，则进行毫秒内序列
        if (lastTimestamp == timestamp) {
            sequence = (sequence + 1) & SEQUENCE_MASK;
            // 毫秒内序列溢出 即 序列 > 4095
            if (sequence == 0) {
                // 阻塞到下一个毫秒,获得新的时间戳
                timestamp = blockTillNextMillis(lastTimestamp);
            }
        }
        // 时间戳改变，毫秒内序列重置
        else {
            sequence = 0L;
        }
        // 上次生成ID的时间截
        lastTimestamp = timestamp;
        // 移位并通过或运算拼到一起组成64位的ID
        return ((timestamp - START_TIME) << TIMESTAMP_MOVE_BITS)
                | (dataCenterId << DATA_CENTER_ID_MOVE_BITS)
                | (workerId << WORKER_ID_MOVE_BITS)
                | sequence;
    }

    protected long blockTillNextMillis(long lastTimestamp) {
        long timestamp = currentTime();
        while (timestamp <= lastTimestamp) {
            timestamp = currentTime();
        }
        return timestamp;
    }

    /**
     * 获得以毫秒为单位的当前时间
     *
     * @return long
     * @date 2023/8/3
     */
    private long currentTime() {
        return System.currentTimeMillis();
    }
}
