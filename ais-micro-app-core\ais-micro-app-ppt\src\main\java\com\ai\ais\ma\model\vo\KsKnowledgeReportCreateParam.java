package com.ai.ais.ma.model.vo;

import com.ai.ais.ma.enums.KsReportType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <p>
 * 知识报告表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
@Getter
@Setter
@Schema(description = "知识报告表创建参数")
public class KsKnowledgeReportCreateParam {

    @Schema(description = "名称")
    @Size(message = "名称超过最大长度500的限制", max = 500)
    @NotEmpty(message = "名称不能为空")
    private String name;

    @Schema(description = "用户原始主题")
    @NotEmpty(message = "用户原始主题不能为空")
    private String themeText;

    @Schema(description = "文章内容")
    private String content;

    @Schema(description = "文章内容格式")
    private KsReportType type = KsReportType.MARKDOWN;

    @Schema(description = "大纲结构")
    private List<KsReportOutline> outlineData;

    @Schema(description = "智能问答选择项")
    private KsKnwlChose chose;
}
