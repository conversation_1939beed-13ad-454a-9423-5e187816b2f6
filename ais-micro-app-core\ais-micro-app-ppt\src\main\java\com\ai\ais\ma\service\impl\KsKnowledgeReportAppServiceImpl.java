package com.ai.ais.ma.service.impl;

import com.ai.ais.ma.base.service.impl.BaseAppServiceByCodeImpl;
import com.ai.ais.ma.common.context.RequestContext;
import com.ai.ais.ma.common.sse.SseEmitterBuilder;
import com.ai.ais.ma.utils.sse.GsContentStreamWatcher;
import com.ai.ais.ma.utils.sse.GsOutlineStreamWatcher;
import com.ai.ais.ma.common.utils.BizAssert;
import com.ai.ais.ma.common.utils.IPageUtils;
import com.ai.ais.ma.common.utils.IdGenerator;
import com.ai.ais.ma.common.utils.SortUtils;
import com.ai.ais.ma.config.property.KsGlobalConfig;
import com.ai.ais.ma.convert.vo.KsKnowledgeReportVoConverter;
import com.ai.ais.ma.infra.po.KsKnowledgeReportPO;
import com.ai.ais.ma.model.dto.KsKnowledgeReportDTO;
import com.ai.ais.ma.model.vo.*;
import com.ai.ais.ma.model.vo.report.KsReportOutlineQueryParam;
import com.ai.ais.ma.repository.KsKnowledgeReportRepository;
import com.ai.ais.ma.rpc.service.GsPromptRpcService;
import com.ai.ais.ma.service.KsKnowledgeReportAppService;
import com.alibaba.ttl.TtlRunnable;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.chinatelecom.gs.engine.kms.sdk.enums.DefaultPromptCodeType;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.report.*;
import com.chinatelecom.gs.engine.kms.sdk.vo.prompt.PromptRequest;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;

/**
 * <p>
 * 知识报告表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
@Service
@Validated
@Slf4j
public class KsKnowledgeReportAppServiceImpl extends BaseAppServiceByCodeImpl<KsKnowledgeReportRepository, KsKnowledgeReportQueryParam, KsKnowledgeReportVoConverter, KsKnowledgeReportPO, KsKnowledgeReportDTO, KsKnowledgeReportVO, KsKnowledgeReportCreateParam, KsKnowledgeReportUpdateParam> implements KsKnowledgeReportAppService {

    private final static String CONTENT = "##CONTENT##";
    @Resource
    private GsPromptRpcService gsPromptRpcService;

    @Resource
    private KsGlobalConfig ksGlobalConfig;

    @Resource
    @Qualifier("ssePoolExecutor")
    private ExecutorService ssePoolExecutor;


    @Override
    protected KsKnowledgeReportVoConverter converter() {
        return KsKnowledgeReportVoConverter.INSTANCE;
    }

    String outlinePrompt="你是用户的PPT大纲生成助手，请根据下列主题生成章节结构。\n" +
            "\n" +
            "输出格式为：\n" +
            "# PPT标题（只有一个）\n" +
            "## 章的名字\n" +
            "### 节的名字\n" +
            "- 内容1\n" +
            "- 内容2\n" +
            "- 内容3\n" +
            "### 节的名字\n" +
            "- xxxxx\n" +
            "- xxxxx\n" +
            "- xxxxx\n" +
            "### 节的名字\n" +
            "- xxxxx\n" +
            "- xxxxx\n" +
            "- xxxxx\n" +
            "\n" +
            "这是生成要求：{require}\\n\n" +
            "这是生成的语言要求：{language}";

    String contentPrompt="你是一个专业的PPT内容生成助手，请根据给定的大纲内容，生成完整的PPT页面内容结构。\n" +
            "\n" +
            "页面类型包括：\n" +
            "- 封面页：\"cover\"\n" +
            "- 目录页：\"contents\"\n" +
            "- 内容页：\"content\"\n" +
            "- 过渡页：\"transition\"\n" +
            "- 结束页：\"end\"\n" +
            "\n" +
            "输出格式要求如下：\n" +
            "- 每一页为一个独立 JSON 对象\n" +
            "- 每个 JSON 对象写在**同一行**\n" +
            "- 页面之间用两个换行符分隔\n" +
            "- 不要添加任何注释或解释说明\n" +
            "\n" +
            "示例格式（注意每个 JSON 占一行）：\n" +
            "\n" +
            "{{\"type\": \"cover\", \"data\": {{ \"title\": \"接口相关内容介绍\", \"text\": \"了解接口定义、设计与实现要点\" }}}}\n" +
            "\n" +
            "{{\"type\": \"contents\", \"data\": {{ \"items\": [\"接口定义概述\", \"接口分类详情\", \"接口设计原则\"] }}}}\n" +
            "\n" +
            "{{\"type\": \"transition\", \"data\": {{ \"title\": \"接口定义\", \"text\": \"开始介绍接口的基本含义\" }}}}\n" +
            "\n" +
            "{{\"type\": \"content\", \"data\": {{ \"title\": \"接口定义\", \"items\": [ {{ \"title\": \"基本概念\", \"text\": \"接口是系统中模块通信的协议\" }}, {{ \"title\": \"作用\", \"text\": \"促进模块解耦，提高系统灵活性\" }} ] }}}}\n" +
            "\n" +
            "{{\"type\": \"end\"}}\n" +
            "\n" +
            "请根据以下信息生成 PPT 内容：\n" +
            "\n" +
            "语言：{language}\n" +
            "大纲内容：{content}";

    /**
     * 构造分页查询条件, 有额外条件覆盖该方法
     *
     * @param query
     * @return
     */
    protected Wrapper<KsKnowledgeReportPO> pageQueryWrapper(KsKnowledgeReportQueryParam query) {
        LambdaQueryWrapper<KsKnowledgeReportPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .select(KsKnowledgeReportPO.class, i -> i.getTypeHandler() != JacksonTypeHandler.class)
                .like(StringUtils.isNotBlank(query.getName()), KsKnowledgeReportPO::getName, query.getName())
                .eq(KsKnowledgeReportPO::getCreateId, RequestContext.getUserId());
        SortUtils.buildOrderBy(lambdaQueryWrapper, query.getOrder());
        return lambdaQueryWrapper;
    }


    @Override
    public Page<KsKnowledgeReportVO> pageQuery(KsKnowledgeReportQueryParam query) {
        IPage<KsKnowledgeReportDTO> page = new PageDTO<>(query.getPageNum(), query.getPageSize());
        Wrapper<KsKnowledgeReportPO> queryWrapper = pageQueryWrapper(query);
        page = repository.page(page, queryWrapper);
        return IPageUtils.convertBatch(page, dto -> toVOS(dto));
    }

    @Override
    public KsKnowledgeReportVO create(KsKnowledgeReportCreateParam createParam) {
        KsKnowledgeReportDTO dto = converter().convertCreate(createParam);
        if (StringUtils.isBlank(dto.getCode())) {
            dto.setCode(IdGenerator.getId(StringUtils.EMPTY));
        }
        repository.save(dto);
        return converter().convertVO(dto);
    }


    private List<KsKnowledgeReportVO> toVOS(List<KsKnowledgeReportDTO> dtoList) {
        List<KsKnowledgeReportVO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dtoList)) {
            for (KsKnowledgeReportDTO dto : dtoList) {
                KsKnowledgeReportVO reportVO = converter().convertVO(dto);
                result.add(reportVO);
            }
        }
        return result;
    }

    @Override
    public boolean update(String code, KsKnowledgeReportUpdateParam updateParam) {
        return repository.updateOneByCode(code, updateParam);
    }


    @Override
    public KsKnowledgeReportVO get(String code) {
        KsKnowledgeReportDTO KsKnowledgeReportDTO = repository.selectByCode(code);
        BizAssert.notNull(KsKnowledgeReportDTO, "A0004", "未查询到知识报告：{}", code);
        return toVO(KsKnowledgeReportDTO);
    }

    private KsKnowledgeReportVO toVO(KsKnowledgeReportDTO knowledgeReportDTO) {
        KsKnowledgeReportVO reportVO = converter().convertVO(knowledgeReportDTO);
        return reportVO;
    }


    @Override
    public SseEmitter outlineSse(KsReportOutlineQueryParam dto) {
        // 构建完整的prompt
        String completePrompt = buildOutlinePromptWithTemplate(dto);

        PromptRequest outlineRequest = PromptRequest.builder()
                .code(DefaultPromptCodeType.OUTLINE.name())
                .prompt(completePrompt)
                .build();

        SseEmitter sseEmitter = SseEmitterBuilder.buildSseEmitter();

        // 使用自定义的大纲流式处理器
        outlineStreamPrompt(sseEmitter, outlineRequest);

        return sseEmitter;
    }

    /**
     * 构建带模板替换的大纲prompt
     */
    private String buildOutlinePromptWithTemplate(KsReportOutlineQueryParam dto) {
        String template = outlinePrompt;
        // 从dto中获取语言，如果没有则默认为中文
        String language = "中文"; // 默认语言
        String require = dto.getThemeText();

        // 进行模板替换
        template = template.replace("{require}", require);
        template = template.replace("{language}", language);

        return template;
    }

    /**
     * 大纲流式调用处理
     */
    private void outlineStreamPrompt(SseEmitter emitter, PromptRequest request) {
        ssePoolExecutor.execute(TtlRunnable.get(() -> {
            try {
                CountDownLatch latch = new CountDownLatch(1);
                GsOutlineStreamWatcher watcher = new GsOutlineStreamWatcher(latch);
                watcher.setSseEmitter(emitter);
                watcher.request(request);
                latch.await();
            } catch (Exception e) {
                log.error("大纲sse请求异常", e);
            } finally {
                emitter.complete();
            }
        }));
    }


    private List<ReportOutline> dfsTree(List<ReportOutline> outlineRes) {
        List<ReportOutline> result = new ArrayList<>();

        Stack<ReportOutline> stack = new Stack<>();

        List<ReportOutline> reversedList = new ArrayList<>(outlineRes);
        Collections.reverse(reversedList);

        reversedList.forEach(t -> stack.push(t));
        while (!stack.isEmpty()) {
            ReportOutline outline = stack.pop();
            result.add(outline);

            List<ReportOutline> children = outline.getChildren();
            if (CollectionUtils.isNotEmpty(children)) {
                List<ReportOutline> reversedChildren = new ArrayList<>(children);
                Collections.reverse(reversedChildren);
                reversedChildren.forEach(t -> stack.push(t));
            }
        }
        return result;
    }





    private PromptRequest buildPromptRequest(ReportOutlineQueryParam dto, Map<String, Object> promptParam) {
        PromptRequest result = null;
        OperateSourceType source = dto.getSource();
        String reportLlmModelCode = ""; // 默认模型代码

        switch (source) {
            case INIT:
                result = PromptRequest.builder().code(DefaultPromptCodeType.OUTLINE.name())
                        .promptParam(promptParam).build();
                break;
            case RETRY:
                result = PromptRequest.builder().code(DefaultPromptCodeType.OUTLINE_REWRITE.name())
                        .promptParam(promptParam).build();
                break;
        }
        return result;
    }


    @Override
    public SseEmitter content(ReportContentQueryParam param) throws IOException {
        // 构建完整的prompt，进行模板替换
        String completePrompt = buildContentPromptWithTemplate(param);

        PromptRequest request = PromptRequest.builder()
                .code(DefaultPromptCodeType.CONTENT.name())
                .prompt(completePrompt)
                .build();

        // 创建SSE发射器
        SseEmitter sseEmitter = SseEmitterBuilder.buildSseEmitter();

        // 使用自定义的正文流式处理器
        contentStreamPrompt(sseEmitter, request);

        return sseEmitter;
    }

    /**
     * 构建带模板替换的正文prompt
     */
    private String buildContentPromptWithTemplate(ReportContentQueryParam param) {
        String template = contentPrompt;
        String language = "中文"; // 默认语言
        String content = param.getOutline();

        // 进行模板替换
        template = template.replace("{language}", language);
        template = template.replace("{content}", content);

        return template;
    }

    /**
     * 正文流式调用处理
     */
    private void contentStreamPrompt(SseEmitter emitter, PromptRequest request) {
        ssePoolExecutor.execute(TtlRunnable.get(() -> {
            try {
                CountDownLatch latch = new CountDownLatch(1);
                GsContentStreamWatcher watcher = new GsContentStreamWatcher(latch);
                watcher.setSseEmitter(emitter);
                watcher.request(request);
                latch.await();
            } catch (Exception e) {
                log.error("正文sse请求异常", e);
            } finally {
                emitter.complete();
            }
        }));
    }

    @Override
    public ReportOutlineVO outline(KsReportOutlineQueryParam dto) {

        ReportOutlineVO result = new ReportOutlineVO();
        String outline = null;
        String headline = dto.getThemeText();

        try {
            PromptRequest outlineRequest = buildOutlineRequest(dto);
            outline = gsPromptRpcService.promptSync(outlineRequest);
            log.info("大模型返回结果：{}", outline);

        } catch (Exception e) {
            log.error("请求大模型异常", e);
            throw new BizException(e, "C0003", "请求大模型异常");
        }

        result.setHeadLine(headline);
        if (StringUtils.isNotBlank(outline)) {
            List<ReportOutline> outlines = parseMarkdownPPT(outline);
            result.setOutlineRes(outlines);
        } else {
            BizAssert.throwBizException("C0005", "调用gs-engine生成大纲异常");
        }
        return result;
    }

    /**
     * 构造大纲请求参数
     *
     * @param dto
     * @return
     */
    private PromptRequest buildOutlineRequest(KsReportOutlineQueryParam dto) {
        OperateSourceType source = dto.getSource();
        PromptRequest outlineRequest = null;
        outlineRequest = PromptRequest.builder().code(DefaultPromptCodeType.OUTLINE.name())
                .prompt(outlinePrompt+dto.getThemeText()).build();
        return outlineRequest;
    }

    /**
     * 解析markdown大纲
     *
     * @param markdown
     * @return
     */
    private static List<ReportOutline> parseMarkdownPPT(String markdown) {
        if (markdown == null || markdown.isEmpty()) {
            return new ArrayList<>();
        }

        // 预处理markdown文本，在标题前添加换行符
        String processedMarkdown = preprocessMarkdown(markdown);
        String[] lines = processedMarkdown.split("\n");

        List<ReportOutline> outline = new ArrayList<>();
        ReportOutline currentLevel1 = null;
        ReportOutline currentLevel2 = null;
        ReportOutline currentLevel3 = null;

        for (String line : lines) {
            line = line.trim();
            if (StringUtils.isBlank(line)) {
                continue;
            }

            try {
                if (line.startsWith("# ")) {
                    String title = line.substring(2).trim();
                    if (StringUtils.isNotBlank(title)) {
                        currentLevel1 = ReportOutline.builder()
                                .title(title)
                                .children(new ArrayList<>())
                                .build();
                        outline.add(currentLevel1);
                        currentLevel2 = null;
                        currentLevel3 = null;
                    }
                } else if (line.startsWith("## ")) {
                    String title = line.substring(3).trim();
                    if (StringUtils.isNotBlank(title) && currentLevel1 != null) {
                        currentLevel2 = ReportOutline.builder()
                                .title(title)
                                .children(new ArrayList<>())
                                .build();
                        if (currentLevel1.getChildren() != null) {
                            currentLevel1.getChildren().add(currentLevel2);
                        }
                        currentLevel3 = null;
                    }
                } else if (line.startsWith("### ")) {
                    String title = line.substring(4).trim();
                    if (StringUtils.isNotBlank(title) && currentLevel2 != null) {
                        currentLevel3 = ReportOutline.builder()
                                .title(title)
                                .children(new ArrayList<>())
                                .build();
                        if (currentLevel2.getChildren() != null) {
                            currentLevel2.getChildren().add(currentLevel3);
                        }
                    }
                } else if (line.startsWith("- ")) {
                    String title = line.substring(2).trim();
                    if (StringUtils.isNotBlank(title) && currentLevel3 != null) {
                        ReportOutline content = ReportOutline.builder()
                                .title(title)
                                .children(new ArrayList<>())
                                .build();
                        if (currentLevel3.getChildren() != null) {
                            currentLevel3.getChildren().add(content);
                        }
                    }
                }
            } catch (Exception e) {
                // 忽略解析错误，继续处理下一行
            }
        }
        return outline;
    }

    /**
     * 预处理markdown文本，在标题标记前添加换行符
     */
    private static String preprocessMarkdown(String markdown) {
        if (StringUtils.isBlank(markdown)) {
            return markdown;
        }

        // 在 ##、###、- 前添加换行符，但不要在开头添加
        String processed = markdown;
        processed = processed.replaceAll("(?<!^)(?<!\\n)(##)", "\n$1");
        processed = processed.replaceAll("(?<!^)(?<!\\n)(###)", "\n$1");
        processed = processed.replaceAll("(?<!^)(?<!\\n)(-\\s)", "\n$1");

        return processed;
    }



    @Override
    public void rename(String code, KsKnowledgeRenameParam dto) {
        KsKnowledgeReportDTO knowledgeReportDTO = repository.selectByCode(code);
        BizAssert.notNull(knowledgeReportDTO, "A0004", "知识报告不存在");
        repository.updateName(code, dto);
    }

}
