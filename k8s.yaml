apiVersion: apps/v1
kind: Deployment
metadata:
  name: ais-ks
  namespace: chengdu-ai-kms-dev
  labels:
    app: ais-ks
    app.kubernetes.io/name: chengdu-ai-kms-dev
  annotations:
    sidecar.istio.io/inject: 'true'
spec:
  replicas: 1
  selector:
    matchLabels:
      name: ais-ks
  template:
    metadata:
      labels:
        name: ais-ks
    spec:
      automountServiceAccountToken: false  # 禁用服务账户令牌的自动挂载
      imagePullSecrets:
        - name: chengdu-ai-kms-dev-registry-key
      containers:
        - name: ais-ks
          image: artifact.srdcloud.cn/ct_aics-snapshot-docker-local/ais-ks:{{.Tag}}
          imagePullPolicy: Always
          command:
            - /bin/bash
            - -c
            - "./bin/start.sh"
          ports:
            - containerPort: 8066
              name: web-port
          livenessProbe:
            httpGet:
              path: /ais/ks/ping
              port: 8066
              scheme: HTTP
            initialDelaySeconds: 360
            periodSeconds: 2
            failureThreshold: 3
            successThreshold: 1
            timeoutSeconds: 20
          readinessProbe:
            httpGet:
              path: /ais/ks/ping
              port: 8066
              scheme: HTTP
            timeoutSeconds: 1
            periodSeconds: 1
            successThreshold: 1
            failureThreshold: 3
          lifecycle:
            preStop:
              exec:
                command: ["/bin/bash", "-c", "sleep 30"]
          env:
            - name: "JASYPT_PASSWORD"
              valueFrom:
                secretKeyRef:
                  name: jasypt-pass
                  key: JASYPT_PASSWORD
            - name: "JAVA_OPS"
              value: "-Xms256m -Xmx2048m -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/usr/src/app/telecom/heapError "
            - name: "JAVA_AGENT"
              value: "-Duser.language=zh -Duser.country=CN -Duser.timezone=Asia/Shanghai -Dfile.encoding=utf-8 "
            - name: "NACOS_ADDR"
              value: "10.127.188.58:31751"
            - name: "NACOS_NAMESPACE"
              value: "a69dd065-e232-4a94-aa70-52df1373a751"
            - name: "NACOS_USERNAME"
              value: "nacos"
            - name: "NACOS_PASSWORD"
              value: "ENC(8SkLvWoRvbPMQqdlb4CGuiWsFSPIJ8RZO0/+T5L7RkR6d+GPQRpN9GXyq1e/AE9I)"
            - name: "NACOS_ENABLED"
              value: 'true'
            - name: "PROFILE"
              value: "prod"

---

apiVersion: v1
kind: Service
metadata:
  name: ais-ks
  namespace: chengdu-ai-kms-dev
spec:
  type: ClusterIP
  ports:
    - port: 8066
      targetPort: 8066
  selector:
    name: ais-ks
