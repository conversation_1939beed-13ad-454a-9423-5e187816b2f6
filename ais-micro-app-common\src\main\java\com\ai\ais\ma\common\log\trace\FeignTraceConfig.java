package com.ai.ais.ma.common.log.trace;

import com.ai.ais.ma.common.log.utils.MDCTraceUtils;
import feign.RequestInterceptor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * feign拦截器，传递traceId
 */
@Configuration
@ConditionalOnClass(value = {RequestInterceptor.class})
public class FeignTraceConfig {

    @Value(("${app.trace.enable:false}"))
    private Boolean traceEnable;

    @Bean
    public RequestInterceptor feignTraceInterceptor() {
        return template -> {
            if (traceEnable) {
                //传递日志traceId
                String traceId = MDCTraceUtils.getTraceId();
                if (StringUtils.isNotEmpty(traceId)) {
                    template.header(MDCTraceUtils.TRACE_ID_HEADER, traceId);
                    template.header(MDCTraceUtils.SPAN_ID_HEADER, MDCTraceUtils.getNextSpanId());
                }
            }
        };
    }
}
