package com.ai.ais.ma.common.log.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Arrays;

/**
 * ReflectDeclaredFieldUtils
 *
 * <AUTHOR>
 * @date 2022-11-12 10:09
 */
@Slf4j
public class ReflectDeclaredFieldUtils {

    private ReflectDeclaredFieldUtils() {
        throw new IllegalStateException("Utility class");
    }


    /**
     * 更新指定类的属性 返回原来的值
     *
     * @param object    Object
     * @param fieldName String
     * @param newValue  Object
     */
    public static void declaredFieldUpdate(Object object, final String fieldName, Object newValue) throws ReflectiveOperationException {
        Field prosField = object.getClass().getDeclaredField(fieldName);
        Field modifiers = Field.class.getDeclaredField("modifiers");
        ReflectionUtils.makeAccessible(modifiers);
        ReflectionUtils.setField(modifiers, prosField, modifiers.getInt(prosField) & (~Modifier.FINAL));
        ReflectionUtils.makeAccessible(prosField);
        ReflectionUtils.setField(prosField, object, newValue);
    }


    /**
     * 通过指定的构造参数方法创建实例，这里会先把构造方法改为 PUBLIC 后再调用
     *
     * @param clazz      要创建的类的class
     * @param parameters 构造函数的参数
     */
    public static <T> T getInstanceByConstructor(Class<T> clazz, Object... parameters) throws ReflectiveOperationException {
        Constructor<T> declaredConstructor = clazz.getDeclaredConstructor(Arrays.stream(parameters).map(Object::getClass).toArray(Class[]::new));
        Field modifiers = Constructor.class.getDeclaredField("modifiers");
        ReflectionUtils.makeAccessible(modifiers);
        ReflectionUtils.setField(modifiers, declaredConstructor, Modifier.PUBLIC);
        return declaredConstructor.newInstance(parameters);
    }
}
