package com.ai.ais.ma.config.cache.config;


import com.ai.ais.ma.common.utils.JsonUtils;
import com.ai.ais.ma.config.cache.customized.CustomizedRedisCacheManager;
import com.ai.ais.ma.config.cache.lock.impl.RedissonDistributedLock;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.BatchStrategies;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.*;

import java.text.SimpleDateFormat;
import java.time.Duration;

/**
 * RedisCacheConfig
 *
 * <AUTHOR>
 * @date 2023-05-29 10:06
 */
@Configuration
@EnableCaching(order = 10)
@ConditionalOnProperty(prefix = "ks.cache", name = "type", havingValue = "REDIS")
public class RedisCacheConfig {

    @Value("${spring.redis.scan-batch-size:10000}")
    private Integer scanBatchSize;

    @Bean
    @Primary
//    @ConditionalOnSingleCandidate(RedisConnectionFactory.class)
    public RedisTemplate<String, Object> redisTemplate(@Autowired(required = false) RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redisConnectionFactory);

        // 设置键（key）的序列化方式
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());

        // 设置值（value）的序列化方式
        redisTemplate.setValueSerializer(new GenericToStringSerializer<>(Object.class));
        redisTemplate.setHashValueSerializer(new GenericToStringSerializer<>(Object.class));

        redisTemplate.setEnableTransactionSupport(true);
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }

    /**
     * redis缓存
     *
     * @return
     */
    @Bean
    @Primary
//    @ConditionalOnSingleCandidate(RedisConnectionFactory.class)
    public CustomizedRedisCacheManager getRedisCacheManager(@Autowired(required = false) RedisConnectionFactory redisConnectionFactory) {
        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory, BatchStrategies.scan(scanBatchSize));
        CustomizedRedisCacheManager redisCacheManager = new CustomizedRedisCacheManager(redisCacheWriter, getRedisCacheConfig());
        return redisCacheManager;
    }


    private RedisCacheConfiguration getRedisCacheConfig() {
        // todo jackson序列化还不能正常work
//        RedisSerializer<String> redisKeySerializer = new StringRedisSerializer();
//        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = redisValueSerializer();
        // 配置序列化，解决乱码的问题，设置缓存名称的前缀和缓存条目的默认过期时间
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
                // 设置键的序列化器
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(keySerializer()))
                // 设置值的序列化器
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(redisValueSerializer()))
                // 设置缓存名称的前缀
//                .computePrefixWith(name -> "gs-" + name + ":")
                // 设置缓存条目的默认过期时间为300秒
                .entryTtl(Duration.ofHours(1))
                .disableCachingNullValues();
        return config;
    }

    /**
     * 配置Redis键的序列化方式
     *
     * @return StringRedisSerializer，用于序列化和反序列化Redis中的键
     */
    private RedisSerializer<String> keySerializer() {
        return new StringRedisSerializer();
    }

    private Jackson2JsonRedisSerializer<Object> redisValueSerializer() {
        ObjectMapper objectMapper = JsonUtils.newInstance();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL, JsonTypeInfo.As.WRAPPER_ARRAY);

        // 注册 JavaTimeModule 以支持 Java 8 日期/时间类型
        objectMapper.registerModule(new JavaTimeModule());

        // 设置时区和日期格式（可选）
        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX"));

        // 创建并返回 Jackson2JsonRedisSerializer 实例
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
        jackson2JsonRedisSerializer.setObjectMapper(objectMapper);

        return jackson2JsonRedisSerializer;
    }

    @Bean
    @Primary
    @ConditionalOnClass(RedissonClient.class)
    RedissonDistributedLock redisCache() {
        return new RedissonDistributedLock();
    }

}
