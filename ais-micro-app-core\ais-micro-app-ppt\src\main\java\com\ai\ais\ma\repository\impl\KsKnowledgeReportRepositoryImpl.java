package com.ai.ais.ma.repository.impl;

import com.ai.ais.ma.base.infra.repository.impl.BaseByCodeRepositoryImpl;
import com.ai.ais.ma.common.utils.JsonUtils;
import com.ai.ais.ma.convert.dto.KsKnowledgeReportDtoConverter;
import com.ai.ais.ma.convert.vo.CommonConverter;
import com.ai.ais.ma.infra.mapper.KsKnowledgeReportMapper;
import com.ai.ais.ma.infra.po.KsKnowledgeReportPO;
import com.ai.ais.ma.model.dto.KsKnowledgeReportDTO;
import com.ai.ais.ma.model.vo.KsKnowledgeRenameParam;
import com.ai.ais.ma.model.vo.KsKnowledgeReportUpdateParam;
import com.ai.ais.ma.repository.KsKnowledgeReportRepository;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinatelecom.gs.engine.kms.sdk.vo.base.BaseNameVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 知识报告表 Repository接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */


@Service
@Slf4j
public class KsKnowledgeReportRepositoryImpl extends BaseByCodeRepositoryImpl<KsKnowledgeReportMapper, KsKnowledgeReportPO, KsKnowledgeReportDTO> implements KsKnowledgeReportRepository {

    @Override
    public KsKnowledgeReportDTO convertToDto(KsKnowledgeReportPO source) {
        return KsKnowledgeReportDtoConverter.INSTANCE.convert(source);
    }

    @Override
    public KsKnowledgeReportPO convertToPo(KsKnowledgeReportDTO source) {
        return KsKnowledgeReportDtoConverter.INSTANCE.convert(source);
    }



    @Override
    public void updateName(String code, KsKnowledgeRenameParam updateParam) {
        LambdaUpdateWrapper<KsKnowledgeReportPO> wrapper = new LambdaUpdateWrapper<>();
        String name = ObjectUtils.defaultIfNull(updateParam.getName(), StringUtils.EMPTY);
        wrapper.set(KsKnowledgeReportPO::getName, name)
                .eq(KsKnowledgeReportPO::getCode, code)
                .last(getOnlyOneSql());
        this.getBaseMapper().update(wrapper);
    }


    @Override
    public List<BaseNameVO> listNameAndCodeByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<KsKnowledgeReportPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(KsKnowledgeReportPO::getName, KsKnowledgeReportPO::getCode, KsKnowledgeReportPO::getId,
                        KsKnowledgeReportPO::getCreateId, KsKnowledgeReportPO::getCreateName, KsKnowledgeReportPO::getCreateTime,
                        KsKnowledgeReportPO::getUpdateId, KsKnowledgeReportPO::getUpdateName, KsKnowledgeReportPO::getUpdateTime)
                .in(KsKnowledgeReportPO::getCode, codes);
        List<KsKnowledgeReportPO> poList = getBaseMapper().selectList(queryWrapper);
        return CommonConverter.INSTANCE.convertReport(poList);
    }

    @Override
    public boolean updateOneByCode(String code, KsKnowledgeReportUpdateParam updateParam) {
        LambdaUpdateWrapper<KsKnowledgeReportPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(StringUtils.isNotBlank(updateParam.getName()), KsKnowledgeReportPO::getName, updateParam.getName())
                .set(StringUtils.isNotBlank(updateParam.getThemeText()), KsKnowledgeReportPO::getThemeText, updateParam.getThemeText())
                .set(StringUtils.isNotBlank(updateParam.getContent()), KsKnowledgeReportPO::getContent, updateParam.getContent())
                .set(updateParam.getType() != null, KsKnowledgeReportPO::getType, updateParam.getType())
                .set(updateParam.getOutlineData() != null, KsKnowledgeReportPO::getOutlineData, JsonUtils.toJsonString(updateParam.getOutlineData()))
                .eq(KsKnowledgeReportPO::getCode, code)
                .last(getOnlyOneSql());
        return SqlHelper.retBool(this.getBaseMapper().update(wrapper));
    }



}

