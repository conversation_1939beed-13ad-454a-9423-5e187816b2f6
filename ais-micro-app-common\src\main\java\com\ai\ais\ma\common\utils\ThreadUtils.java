package com.ai.ais.ma.common.utils;

import com.ai.ais.ma.config.property.KsGlobalConfig;
import com.alibaba.ttl.threadpool.TtlExecutors;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

public class ThreadUtils {

    public static ThreadFactory newGenericThreadFactory(String processName) {
        return newGenericThreadFactory(processName, false);
    }

    public static ThreadFactory newGenericThreadFactory(final String processName, final boolean isDaemon) {
        return new ThreadFactory() {
            private AtomicInteger threadIndex = new AtomicInteger(0);

            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r, String.format("%s-%d", processName, this.threadIndex.incrementAndGet()));
                thread.setDaemon(isDaemon);
                return thread;
            }
        };
    }

    public static ExecutorService newPool(KsGlobalConfig.ThreadPoolConfig config) {
        BlockingQueue<Runnable> queue;
        if (config.getQueueSize() <= 0) {
            queue = new LinkedBlockingQueue<>();
        } else {
            queue = new ArrayBlockingQueue<>(config.getQueueSize());
        }
        ExecutorService service = new ThreadPoolExecutor(config.getCoreSize(), config.getMaxSize(), 60, TimeUnit.SECONDS,
                queue, ThreadUtils.newGenericThreadFactory(config.getName()));
        return TtlExecutors.getTtlExecutorService(service);
    }
}
