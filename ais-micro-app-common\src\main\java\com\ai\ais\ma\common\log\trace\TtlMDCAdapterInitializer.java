package com.ai.ais.ma.common.log.trace;

import com.ai.ais.ma.common.log.slf4j.TtlMDCAdapter;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * 初始化TtlMDCAdapter实例，并替换MDC中的adapter对象
 *
 * <AUTHOR>
 */
@Component
public class TtlMDCAdapterInitializer implements ApplicationContextAware {

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        //加载TtlMDCAdapter实例
        TtlMDCAdapter.getInstance();
    }
}
