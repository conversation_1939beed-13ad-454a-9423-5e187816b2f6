package com.ai.ais.ma.base.dto;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * BaseEntity
 *
 * <AUTHOR>
 * @date 2023-04-03 19:06
 */
@Getter
@Setter
public class RootDTO {

    /**
     * 主键
     */
    protected Long id;


    /**
     * 删除标记,1:删除,0:未删除
     */
    protected Long yn;

    /**
     * 创建用户ID
     */
    protected String createId;

    /**
     * 创建用户名
     */
    protected String createName;

    /**
     * 创建时间
     */
    protected LocalDateTime createTime;

    /**
     * 最后修改用户ID
     */
    protected String updateId;

    /**
     * 最后修改用户名
     */
    protected String updateName;

    /**
     * 更新时间
     */
    protected LocalDateTime updateTime;

}
