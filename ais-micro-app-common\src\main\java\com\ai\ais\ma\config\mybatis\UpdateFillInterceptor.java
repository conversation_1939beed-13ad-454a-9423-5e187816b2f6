/*
 * Copyright (c) 2011-2022, baomidou .
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.ai.ais.ma.config.mybatis;

import com.ai.ais.ma.common.context.RequestContext;
import com.ai.ais.ma.common.context.RequestInfo;
import com.ai.ais.ma.config.mybatis.handler.UpdateFillHandler;
import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.baomidou.mybatisplus.extension.parser.JsqlParserSupport;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.update.Update;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;

import java.sql.Connection;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 3.4.0
 */
@Data
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@SuppressWarnings({"rawtypes"})
public class UpdateFillInterceptor extends JsqlParserSupport implements InnerInterceptor {

    /**
     * 忽略自动填充数据的配置值,使用方法:在Mapper的方法上添加注解,默认不会忽略
     *
     * @InterceptorIgnore(others = {"noUpdateFill@true"})
     */
    public static final String IGNORE_UPDATE_FILL = "noUpdateFill";

    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private UpdateFillHandler updateFillHandler;

    @Override
    public void beforePrepare(StatementHandler sh, Connection connection, Integer transactionTimeout) {
        PluginUtils.MPStatementHandler mpSh = PluginUtils.mpStatementHandler(sh);
        MappedStatement ms = mpSh.mappedStatement();
        SqlCommandType sct = ms.getSqlCommandType();
        if (sct == SqlCommandType.UPDATE) {
            if (InterceptorIgnoreHelper.willIgnoreOthersByKey(ms.getId(), IGNORE_UPDATE_FILL)) {
                return;
            }

            PluginUtils.MPBoundSql mpBs = mpSh.mPBoundSql();
            mpBs.sql(parserMulti(mpBs.sql(), null));
        }
    }

    /**
     * update 语句处理
     * 这里处理mybatis-plus中没有处理xml中的update自动填充数据的逻辑
     * 这里只能自动更新mybatis-plus包装的数据,xml中的update无法自动更新
     */
    @Override
    protected void processUpdate(Update update, int index, String sql, Object obj) {
        final Table table = update.getTable();
        if (updateFillHandler.ignoreTable(table.getName())) {
            return;
        }
        update.addUpdateSet(new Column(table, "update_time"), new StringValue(dateTimeFormatter.format(LocalDateTime.now())));
        // 处理
        RequestInfo requestInfo = RequestContext.get();
        if (requestInfo == null || Objects.equals(false, requestInfo.getTenant())) {
            return;
        }
        update.addUpdateSet(new Column(table, "update_id"), new StringValue(requestInfo.getUserId()));
        update.addUpdateSet(new Column(table, "update_name"), new StringValue(requestInfo.getUserName()));
    }

}


