package ${package.Parent}.model.vo;
<#if swagger>
    import io.swagger.annotations.ApiModel;
    import io.swagger.annotations.ApiModelProperty;
</#if>
<#if entityLombokModel>
    import lombok.Getter;
    import lombok.Setter;
    <#if chainModel>
        import lombok.experimental.Accessors;
    </#if>
</#if>
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import java.io.Serializable;
import java.time.*;
/**
* <p>
    * ${table.comment!}
    * </p>
*
* <AUTHOR>
* @since ${date}
*/
<#if entityLombokModel>
    @Getter
    @Setter
    <#if chainModel>
        @Accessors(chain = true)
    </#if>
</#if>
<#if swagger>@Schema(description = "${table.comment!}创建参数")
</#if>
public class ${entity}CreateParam {

<#-- ----------  BEGIN 字段循环遍历  ---------->
<#list table.fields as field>
    <#if !field.keyFlag && field.propertyName!="tenantId"&&field.propertyName!="museumId"><#--不是主键、不是租户ID 输出-->
        <#if field.comment!?length gt 0>
            <#if field.metaInfo.nullable>

            <#else>
                @Schema(description ="${field.comment}")
                <#if field.propertyType=="String">
                    @NotEmpty(message = "${field.comment}不能为空")
                <#else>
                    @NotNull(message = "${field.comment}不能为空")
                </#if>
            </#if>
        </#if>
        private ${field.propertyType} ${field.propertyName};
    </#if>
</#list>
<#------------  END 字段循环遍历  ---------->
}
