package com.ai.ais.ma.config.mvc;

import cn.hutool.core.thread.NamedThreadFactory;
import com.alibaba.ttl.threadpool.TtlExecutors;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 公共线程池配置
 *
 * <AUTHOR>
 * @date 2023-05-12 17:18
 */
@Configuration
public class OkHttpClientConfig {

    @Value("${app.httpclient.maxRequest:300}")
    private Integer maxRequest;

    @Value("${app.httpclient.maxRequestPerHost:100}")
    private Integer maxRequestPerHost;

    @Value("${app.httpclient.connectionPoolMaxIdleSize:200}")
    private Integer connectionPoolMaxIdleSize;

    @Value("${app.httpclient.connectionPoolKeepAliveDuration:5}")
    private Long connectionPoolKeepAliveDuration;

    @Value("${app.httpclient.connectTimeout:60}")
    private Integer connectTimeout;

    @Value("${app.httpclient.callTimeout:60}")
    private Integer callTimeout;

    @Value("${app.httpclient.readTimeout:60}")
    private Integer readTimeout;

    @Bean
    public OkHttpClient okHttpClient() {
        ExecutorService executorService = new ThreadPoolExecutor(5, 500,
                60, TimeUnit.SECONDS, new ArrayBlockingQueue<>(50000), new NamedThreadFactory("OkHttp", false));
        ExecutorService execute = TtlExecutors.getTtlExecutorService(executorService);
        Dispatcher dispatcher = new Dispatcher(execute);
        dispatcher.setMaxRequests(this.maxRequest);
        dispatcher.setMaxRequestsPerHost(this.maxRequestPerHost);
        return new OkHttpClient().newBuilder()
                .connectionPool(new ConnectionPool(this.connectionPoolMaxIdleSize, this.connectionPoolKeepAliveDuration, TimeUnit.MINUTES))
                .connectTimeout(connectTimeout, TimeUnit.SECONDS)
                .readTimeout(readTimeout, TimeUnit.SECONDS)
                .dispatcher(dispatcher)
                .callTimeout(this.callTimeout, TimeUnit.SECONDS).build();
    }
}
