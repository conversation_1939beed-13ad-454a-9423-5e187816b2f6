package com.ai.ais.ma.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

/**
 * @Author: g<PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-12-16
 * @Description:
 * @Version: 1.0
 */
@Data
public class KsKnowledgeRenameParam {

    @NotEmpty(message = "名称不能为空")
    @Size(message = "名称超过最大长度500的限制", max = 500)
    @Schema(description = "名称")
    private String name;
}
