package com.ai.ais.ma.rpc.api;

import com.ai.ais.ma.rpc.req.BaseConfigQueryParam;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年02月11日
 */
@FeignClient(name = "telecom-ai-gs-engine-srv", url = "${ks.rpc.gsUrl:}", path = "/ais/base/rpc/config")
public interface BaseConfigApi {

    @PostMapping(KmsApis.GET_API)
    Result<Object> getConfigOrDefault(@Validated @RequestBody BaseConfigQueryParam param);

}
