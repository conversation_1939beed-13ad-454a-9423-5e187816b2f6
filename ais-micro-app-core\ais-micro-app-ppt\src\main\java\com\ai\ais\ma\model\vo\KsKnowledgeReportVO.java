package com.ai.ais.ma.model.vo;

import com.ai.ais.ma.enums.KsReportType;
import com.chinatelecom.gs.engine.kms.sdk.vo.base.BaseCodeVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <p>
 * 知识报告表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
@Getter
@Setter
@Schema(description = "知识报告表")
public class KsKnowledgeReportVO extends BaseCodeVO {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "用户原始主题")
    private String themeText;

    @Schema(description = "文章内容")
    private String content;

    @Schema(description = "文章内容格式")
    private KsReportType type = KsReportType.MARKDOWN;

    @Schema(description = "大纲结构")
    private List<KsReportOutline> outlineData;

    @Schema(description = "智能问答选择项")
    private KsKnwlChose chose;
}
