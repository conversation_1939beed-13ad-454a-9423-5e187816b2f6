package com.ai.ais.ma.config.interceptor;

import com.ai.ais.ma.common.context.RequestContext;
import com.ai.ais.ma.common.context.RequestInfo;
import com.ai.ais.ma.common.utils.InterceptorUtils;
import com.ai.ais.ma.config.interceptor.filter.BaseHandlerInterceptor;
import com.ai.ais.ma.config.property.KsGlobalConfig;
import com.chinatelecom.cloud.platform.client.rpc.PlatformUser;
import com.chinatelecom.cloud.platform.client.util.SsoUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月31日
 */
@Component
@Slf4j
public class WebContextInterceptor implements BaseHandlerInterceptor {

    @Resource
    private KsGlobalConfig ksGlobalConfig;

    public boolean preHandle(ServletRequest request, ServletResponse response) throws Exception {
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        RequestInfo requestInfo = new RequestInfo();
        RequestContext.set(requestInfo);

        PlatformUser platformUser = SsoUtil.get();
        if (platformUser == null) {
            log.error("获取用户信息失败");
            InterceptorUtils.writeError((HttpServletResponse) response, "A0006", "用户未登录，获取登录信息失败");
            return false;
        }
        requestInfo.setUserId(platformUser.getUserId());
        requestInfo.setUserName(platformUser.getName());
        requestInfo.setTenantId(platformUser.getCorpCode());
//            requestInfo.setTeam(UserInfoUtils.getUserTeam());

        InterceptorUtils.setAppCode(httpServletRequest, requestInfo);
        InterceptorUtils.setSourceType(ksGlobalConfig, httpServletRequest, requestInfo);
        return true;
    }


    /**
     * 完成后调用
     *
     * @param request
     * @param response
     * @param ex
     * @throws Exception
     */
    public void afterCompletion(ServletRequest request, ServletResponse response, Exception ex) throws Exception {
        RequestContext.remove();
    }
}
