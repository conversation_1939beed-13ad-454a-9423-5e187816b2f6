package com.ai.ais.ma.model.vo;

import com.chinatelecom.gs.engine.kms.sdk.enums.OrderType;
import com.chinatelelcom.gs.engine.sdk.common.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 知识报告表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
@Data
@Schema(description = "知识报告表分页查询参数")
public class KsKnowledgeReportQueryParam extends PageParam {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "名称")
    private OrderType order = OrderType.C_TIME_DESC;

//    @Schema(description = "是否是我创建的")
//    private boolean createdByMe;
}
