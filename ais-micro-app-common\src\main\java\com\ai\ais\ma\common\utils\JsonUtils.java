package com.ai.ais.ma.common.utils;

import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

/**
 * JsonUtils
 *
 * <AUTHOR>
 * @date 2023-04-08 12:21
 */
public class JsonUtils {

    /**
     * 全局共享一个ObjectMapper可能会导致较多的竞争，这里每个线程使用同一个
     */
    private static final Cache<String, ObjectMapper> CACHE = Caffeine.newBuilder().expireAfterWrite(24, TimeUnit.HOURS).maximumSize(1000).build();
    /**
     * yyyy-MM-dd HH:mm:ss
     */
    private static final String PATTERN = "yyyy-MM-dd HH:mm:ss";
    /**
     * yyyy-MM-dd
     */
    private static final String DATE_PATTERN = "yyyy-MM-dd";
    /**
     * HH:mm:ss
     */
    private static final String TIME_PATTERN = "HH:mm:ss";
    /**
     * logger
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(JsonUtils.class);

    private JsonUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 获取默认的ObjectMapper，需要和Springboot中的配置保持一致，避免两边不一致导致各种问题
     *
     * @return ObjectMapper
     */
    public static ObjectMapper newInstance() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.setDateFormat(new SimpleDateFormat(PATTERN));
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(PATTERN)));
        javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(DateTimeFormatter.ofPattern(DATE_PATTERN)));
        javaTimeModule.addSerializer(LocalTime.class, new LocalTimeSerializer(DateTimeFormatter.ofPattern(TIME_PATTERN)));
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern(PATTERN)));
        javaTimeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(DateTimeFormatter.ofPattern(DATE_PATTERN)));
        javaTimeModule.addDeserializer(LocalTime.class, new LocalTimeDeserializer(DateTimeFormatter.ofPattern(TIME_PATTERN)));
        mapper.registerModule(javaTimeModule);
        mapper.setSerializationInclusion(JsonInclude.Include.ALWAYS);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        mapper.configure(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature(), true);
        mapper.configure(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL, true);
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        mapper.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        return mapper;
    }

    private static String getKey() {
        return String.valueOf(Thread.currentThread().getId());
    }

    public static ObjectMapper getObjectMapper() {
        return CACHE.get(getKey(), t -> newInstance());
    }

    public static String toJsonString(Object obj) {
        try {
            if (obj == null) {
                return null;
            }
            return getObjectMapper().writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new BizException("B0001", e.getMessage());
        }
    }

    public static <T> T parseObject(String json, Class<T> clazz) {
        try {
            if (StringUtils.isBlank(json)) {
                return null;
            }
            return getObjectMapper().readValue(json, clazz);
        } catch (JsonProcessingException e) {
            throw new BizException("B0001", e.getMessage());
        }
    }

    /**
     * @param json  String
     * @param clazz Class
     * @return List
     * @date 2023/8/3
     */
    public static <T> List<T> parseObjectArray(String json, Class<T> clazz) {
        try {
            if (StringUtils.isBlank(json)) {
                return Collections.emptyList();
            }
            return getObjectMapper().readValue(json, getObjectMapper().getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (JsonProcessingException e) {
            throw new BizException("B0001", e.getMessage());
        }
    }


    public static JsonNode parseTree(String json) {
        try {
            if (StringUtils.isBlank(json)) {
                return null;
            }
            return getObjectMapper().readTree(json);
        } catch (IOException e) {
            throw new BizException("B0001", e.getMessage());
        }
    }

    public static JsonNode parseTree(byte[] jsonByte) {
        try {
            if (jsonByte == null || jsonByte.length == 0) {
                return null;
            }
            return getObjectMapper().readTree(jsonByte);
        } catch (IOException e) {
            throw new BizException("B0001", e.getMessage());
        }
    }


    public static <T> T parseObject(JsonNode json, Class<T> clazz) {
        try {
            if (json == null) {
                return null;
            }
            return getObjectMapper().treeToValue(json, clazz);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> List<T> parseObjectArray(JsonNode json, Class<T> clazz) {
        try {
            if (json == null || !json.isArray()) {
                return null;
            }
            return getObjectMapper().convertValue(json, getObjectMapper().getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (IllegalArgumentException e) {
            throw new RuntimeException(e);
        }
    }
}
