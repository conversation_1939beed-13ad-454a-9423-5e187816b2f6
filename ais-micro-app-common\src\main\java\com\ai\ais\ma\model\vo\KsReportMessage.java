package com.ai.ais.ma.model.vo;

import com.ai.ais.ma.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.kms.sdk.enums.SseEventType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年05月21日
 */
@Getter
@Setter
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class KsReportMessage<T> {

    private SseEventType eventType = SseEventType.add;

    private String text;

    private String reasoningContent;

    private T extraData;

    public static KsReportMessage ofAdd(String text) {
        return KsReportMessage.builder().eventType(SseEventType.add).text(text).build();
    }

    public static KsReportMessage ofCover(String text) {
        return KsReportMessage.builder().eventType(SseEventType.cover).text(text).build();
    }

    public static KsReportMessage ofFinish(String text) {
        return KsReportMessage.builder().eventType(SseEventType.finish).text(text).build();
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
