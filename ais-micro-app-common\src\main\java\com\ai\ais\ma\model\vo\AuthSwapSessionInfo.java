package com.ai.ais.ma.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: g<PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2025-05-07
 * @Description:
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AuthSwapSessionInfo {

    @Schema(description = "是否使用header中的token")
    private Boolean useHeaderToken;
}
