package com.ai.ais.ma.rpc.req;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class BaseConfigQueryParam {

    /**
     * 业务配置场景
     */
    @NotBlank(message = "配置类型不能为空")
    private String configType;

    /**
     * 业务场景配置唯一标识
     */
    @NotBlank(message = "业务标识不能为空")
    private String businessNo;

}
