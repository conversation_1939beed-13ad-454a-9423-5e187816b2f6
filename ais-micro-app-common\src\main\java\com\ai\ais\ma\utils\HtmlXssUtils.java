package com.ai.ais.ma.utils;


import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.safety.Safelist;
import org.jsoup.select.Elements;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年03月19日
 */
public class HtmlXssUtils {

    /**
     * 允许的富文本标签
     */
    private static final Safelist SAFE_LIST = relaxed();
    private static final String BASE_DOWNLOAD_URL = KmsApis.ROOT_GS + "/base/web/file/download";

    /**
     * 定义允许的富文本标签
     *
     * @return
     */
    public static Safelist relaxed() {
        return new Safelist()
                .addTags(
                        "a", "b", "blockquote", "br", "caption", "cite", "code", "col",
                        "colgroup", "dd", "div", "dl", "dt", "em", "h1", "h2", "h3", "h4", "h5", "h6",
                        "i", "img", "li", "ol", "p", "pre", "q", "small", "span", "strike", "strong",
                        "sub", "sup", "table", "tbody", "td", "tfoot", "th", "thead", "tr", "u",
                        "ul")

                .addAttributes("a", "href", "title")
                .addAttributes("blockquote", "cite")
                .addAttributes("col", "span", "width")
                .addAttributes("colgroup", "span", "width")
                .addAttributes("img", "align", "alt", "height", "src", "title", "width")
                .addAttributes("ol", "start", "type")
                .addAttributes("q", "cite")
                .addAttributes("table", "summary", "width")
                .addAttributes("td", "abbr", "axis", "colspan", "rowspan", "width")
                .addAttributes(
                        "th", "abbr", "axis", "colspan", "rowspan", "scope",
                        "width")
                .addAttributes("ul", "type")
                .addAttributes("span", "style")
                .addAttributes("p", "style")
                ;
    }

    /**
     * 过滤html中非法标签，检查地址是否合法
     *
     * @param html
     * @return
     */
    public static String safe(String html) {
        if (StringUtils.isBlank(html)) {
            return html;
        }

        // 只保留指定的html标签
        html = Jsoup.clean(html, SAFE_LIST);

        /**
         * 检查图片地址的合法性
         */
        checkImg(html);
        return html;
    }

    private static void checkImg(String html) {
        // 检查图片链接是否合法
        Document document = Jsoup.parse(html);
        // 选择所有的 <img> 标签
        Elements images = document.select("img");
        // 遍历所有图片元素
        for (Element image : images) {
            // 获取图片的 src 属性（图片链接）
            String imgSrc = image.attr("src");
            if (!StringUtils.startsWith(imgSrc, BASE_DOWNLOAD_URL)) {
                throw new BizException("A0079", "图片地址只能以{}开头", BASE_DOWNLOAD_URL);
            }
        }
    }

}
