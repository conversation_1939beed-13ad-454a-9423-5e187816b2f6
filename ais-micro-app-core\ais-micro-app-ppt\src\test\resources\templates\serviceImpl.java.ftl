package ${package.ServiceImpl};

import ${package.Entity}.${entity}PO;
import ${package.Service}.${table.serviceName};
import ${superServiceImplClassPackage};

import org.springframework.beans.factory.annotation.Autowired;

import ${package.Parent}.base.service.impl.BaseAppServiceByCodeImpl;
import ${package.Parent}.convert.vo.${entity}VoConverter;
import ${package.Parent}.model.dto.${entity}DTO;
import ${package.Parent}.model.vo.${entity}CreateParam;
import ${package.Parent}.model.vo.${entity}QueryParam;
import ${package.Parent}.model.vo.${entity}UpdateParam;
import ${package.Parent}.model.vo.${entity}VO;
import ${package.Parent}.repository.${entity}Repository;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
* <p>
    * ${table.comment!} 服务实现类
    * </p>
*
* <AUTHOR>
* @since ${date}
*/
@Service
@Validated
@Slf4j

    public class ${table.serviceImplName} extends ${superServiceImplClass}<${entity}Repository, ${entity}QueryParam, ${entity}VoConverter, ${entity}PO, ${entity}DTO,${entity}VO,${entity}CreateParam,${entity}UpdateParam> implements ${table.serviceName} {

    protected ${entity}VoConverter converter() {
        return ${entity}VoConverter.INSTANCE;
    }

    @Override
    protected Wrapper<${entity}PO> pageQueryWrapper(${entity}QueryParam query) {
        LambdaQueryWrapper<${entity}PO> lambdaQueryWrapper = new LambdaQueryWrapper<>();

        return lambdaQueryWrapper;
    }




    }
