package com.ai.ais.ma.base.infra.repository.impl;

import com.ai.ais.ma.base.dto.BaseCodeDTO;
import com.ai.ais.ma.base.infra.BaseCodeEntity;
import com.ai.ais.ma.base.infra.BaseConvertor;
import com.ai.ais.ma.base.infra.BaseExtendMapper;
import com.ai.ais.ma.base.infra.repository.BaseByCodeRepository;
import com.ai.ais.ma.common.utils.IdGenerator;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月09日
 */
public abstract class BaseByCodeRepositoryImpl<SE extends BaseExtendMapper<PO>, PO extends BaseCodeEntity, DTO extends BaseCodeDTO>
        extends BaseRepositoryImpl<SE, PO, DTO> implements BaseByCodeRepository<DTO, PO>, BaseConvertor<DTO, PO> {

    @Override
    public DTO saveByCode(DTO entity) {
        entity.setCode(IdGenerator.id());
        super.save(entity);
        return entity;
    }

    @Override
    public boolean removeByCode(String code) {
        Assert.notNull(code, "操作编码不能为空");
        LambdaQueryWrapper<PO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PO::getCode, code);
        BaseMapper<PO> mapper = getBaseMapper();
        return SqlHelper.retBool(mapper.delete(wrapper));
    }

    @Override
    public boolean removeByCodes(Collection<String> codes) {
        Assert.isTrue(CollectionUtils.isNotEmpty(codes), "操作编码不能为空");
        LambdaQueryWrapper<PO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(PO::getCode, codes);
        BaseMapper<PO> mapper = getBaseMapper();
        return SqlHelper.retBool(mapper.delete(wrapper));
    }

    @Override
    public DTO selectByCode(String code) {
        Assert.notNull(code, "操作编码不能为空");
        LambdaQueryWrapper<PO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PO::getCode, code);
        BaseMapper<PO> mapper = getBaseMapper();
        PO po = mapper.selectOne(wrapper);
        return convertToDto(po);
    }

    @Override
    public List<DTO> listByCodes(Collection<String> codes) {
        Assert.isTrue(CollectionUtils.isNotEmpty(codes), "操作编码不能为空");
        LambdaQueryWrapper<PO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(PO::getCode, codes);
        BaseMapper<PO> mapper = getBaseMapper();
        List<PO> po = mapper.selectList(wrapper);
        return convertToDto(po);
    }

    @Override
    public boolean updateByCode(DTO entity) {
        Assert.notNull(entity.getCode(), "操作编码不能为空");
        LambdaUpdateWrapper<PO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PO::getCode, entity.getCode());
        BaseMapper<PO> mapper = getBaseMapper();
        return SqlHelper.retBool(mapper.update(convertToPo(entity), wrapper));
    }


}
