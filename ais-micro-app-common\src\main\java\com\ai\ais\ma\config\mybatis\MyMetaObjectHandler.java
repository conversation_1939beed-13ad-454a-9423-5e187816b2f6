package com.ai.ais.ma.config.mybatis;

import com.ai.ais.ma.common.context.RequestContext;
import com.ai.ais.ma.common.context.RequestInfo;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;


@Slf4j
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        LocalDateTime time = LocalDateTime.now();
        this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, time);
        this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, time);

        RequestInfo requestInfo = RequestContext.get();
        if (requestInfo == null || Objects.equals(false, requestInfo.getTenant())) {
            return;
        }

        if (requestInfo.getTenantId() != null) {
            this.strictInsertFill(metaObject, "tenantId", String.class, requestInfo.getTenantId());
        }
        this.strictInsertFill(metaObject, "createId", String.class, requestInfo.getUserId());
        this.strictInsertFill(metaObject, "createName", String.class, requestInfo.getUserName());
        this.strictInsertFill(metaObject, "updateId", String.class, requestInfo.getUserId());
        this.strictInsertFill(metaObject, "updateName", String.class, requestInfo.getUserName());
        if (StringUtils.isNotBlank(requestInfo.getAppCode())) {
            this.strictInsertFill(metaObject, "appCode", String.class, requestInfo.getAppCode());
        }
    }

    /**
     * 这里只能自动更新mybatis-plus包装的数据,xml中的update无法自动更新, 统一使用UpdateFillInterceptor拦截器处理
     *
     * @param metaObject 元对象
     */
    @Override
    public void updateFill(MetaObject metaObject) {
    }

}
