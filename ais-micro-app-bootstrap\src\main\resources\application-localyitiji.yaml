
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${app.db.ma.host}:${app.db.ma.port}/${app.db.ma.dbname}?useUnicode=true&characterEncoding=utf8&useSSL=false&allowMultiQueries=true
    username: ${app.db.ma.username}
    password: ${app.db.ma.password}
  redis:
    database: 0
    connect-timeout: 10s
    username: ${app.redis.username}
    password: ${app.redis.password}
    host: ${app.redis.host}
    port: ${app.redis.port}

app:
  db:
    ma:
      host: 127.0.0.1
      port: 3306
      username: root
      password: mysql_password_local
      dbname: ai_kms_ais_ma
  redis:
    username: default
    password: Admin@coc1
    host: 127.0.0.1
    port: 6379

ma:
  system:
    env: prod
    #    version: 2.3.14-arm
    logEnabled: true
  #    customAppName: ""
  #    customLogo: ""
  #    customIcon: ""
  cache:
    type: REDIS
  filterModes:
    publishFilter:
      name: "发布权限过滤"
      description: "发布权限过滤"
      filtered-codes:
        - "manage_doclib_reset"
        - "manage_doclib_batchrelease"
        - "manage_doclib_batchreset"
        - "manage_qalib_release_show"
        - "manage_qalib_batchreset"
        - "manage_qalib_batchrelease"
        - "manage_doclib_release_show"
        - "manage_doclib_release"
        - "manage_qalib_reset"
        - "manage_qalib_release"
        - "release_approval"

    auditFilter:
      name: "审核权限过滤"
      description: "审核权限过滤"
      filtered-codes:
        - "/flow-sub/approval"
        - "/flow-sub/approval/bpmn"
        - "/flow-sub/approval/listener"
        - "/flow-sub/approval/process"
        - "release_approval"
        - "/approval"
        - "/approval/bpmn"
        - "/approval/listener"
        - "/approval/process"

platform:
  client:
    close-platform: false
    dev: false
    mock: false
    #网关端口：31170 （外网地址）
    app-url: http://***************:31170/ais/ks/f/
    #网关端口：31170 （外网地址）
    api-url: http://***************:31170/ais/im/f/impc/share
    # 外网地址
    server-url: http://***************:32088
    # 内网地址
    dmz-url: http://***************:32088
    include-urls: /web/*,/platform/client/*
    exclude-urls: /ping,/rpc/*,/openapi/*,/v3/api-docs,/doc.html,/webjars/*,/swagger-resources/*
    auth-exclude-urls: /*
    app-code: ais-ks
    app-secret: 50231a5a-2fe6-4921-a07c-b2b8ceb172b8
    manager: redis
    open-swap-session: false
    filters:
      - com.chinatelecom.cloud.platform.client.filter.LogoutFilter
      - com.chinatelecom.cloud.platform.client.filter.LoginFilter
