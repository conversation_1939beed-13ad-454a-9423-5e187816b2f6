FROM artifact.srdcloud.cn/ct_aics-release-docker-local/ty-agent:2.5.0 AS ty-agent
FROM openjdk:8-jdk-bullseye AS build

#添加天眼agent
COPY --from=ty-agent /opt/ty/agent/jar/  /usr/src/app/telecom/

COPY bin/start.sh /usr/src/app/telecom/bin/start.sh
COPY bin/stop.sh /usr/src/app/telecom/bin/stop.sh
COPY bin/env.sh /usr/src/app/telecom/bin/env.sh

COPY ais-micro-app-bootstrap/target/ais-micro-app-encrypted.jar /usr/src/app/telecom/ais-micro-app.jar

RUN echo '--pwd jUDjme93kDU_d*df3 --del false' > /usr/src/app/telecom/classfinal.txt
RUN /bin/bash -c 'chmod -R 777  /usr/src/app/telecom'

# 将GIT一些基本信息打包到镜像中，需要配合研发云流水线build参数使用
#--build-arg SRD_COMMIT_SHA=${SRD_COMMIT_SHA}
#--build-arg SRD_TIMESTAMP=${SRD_TIMESTAMP}
#--build-arg SRD_GIT_BRANCH=${SRD_GIT_BRANCH}
#--build-arg SRD_COMMIT_TAG=${SRD_COMMIT_TAG}
ARG SRD_TIMESTAMP
ARG SRD_COMMIT_SHA
ARG SRD_GIT_BRANCH
ARG SRD_COMMIT_TAG
ENV SRD_TIMESTAMP=${SRD_TIMESTAMP}
ENV SRD_COMMIT_SHA=${SRD_COMMIT_SHA}
ENV SRD_GIT_BRANCH=${SRD_GIT_BRANCH}
ENV SRD_COMMIT_TAG=${SRD_COMMIT_TAG}


ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

WORKDIR /usr/src/app/telecom

## 暴露后端项目的 8066 端口
EXPOSE 8066

HEALTHCHECK --interval=10s --timeout=1s --retries=3 CMD curl http://127.0.0.1:8066/ais/ks/ping

# 指定执行用户
RUN useradd --create-home --no-log-init --shell /bin/bash ks
USER ks


ENTRYPOINT ["./bin/start.sh"]
