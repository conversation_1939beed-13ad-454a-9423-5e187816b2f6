package com.ai.ais.ma.model.vo.report;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @Author: gaoxianjun
 * @CreateTime: 2024-12-16
 * @Description:
 * @Version: 1.0
 */
@Data
public class KsReportContentQueryParam {

    @Schema(description = "用户原始主题")
    private String themeText;

    @Schema(description = "生成的大纲(MD格式)")
    @NotEmpty(message = "生成的大纲(MD格式不能为空")
    private String outline;

}
