package com.ai.ais.ma.service.impl;

import com.ai.ais.ma.common.context.RequestContext;
import com.ai.ais.ma.common.utils.BizAssert;
import com.ai.ais.ma.common.utils.JsonUtils;
import com.ai.ais.ma.config.property.KsGlobalConfig;
import com.ai.ais.ma.rpc.api.BaseConfigApi;
import com.ai.ais.ma.rpc.req.BaseConfigQueryParam;
import com.ai.ais.ma.rpc.req.PublishConfig;
import com.ai.ais.ma.service.PermissionService;
import com.chinatelecom.cloud.platform.client.rpc.Menu;
import com.chinatelecom.cloud.platform.client.rpc.Permission;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年11月28日
 */
@Service
@Validated
@Slf4j
public class PermissionServiceImpl implements PermissionService {

    public static final String PUBLISH_CONFIG = "publishConfig";


    @Resource
    private BaseConfigApi baseConfigApi;


    @Resource
    private KsGlobalConfig ksGlobalConfig;

    @Override
    public List<Permission> filterResource(List<Permission> data) {
        Set<String> enabledModes = getFilteredCodes(RequestContext.getTenantId());
        return applyResourceFilters(data, enabledModes);
    }

    @Override
    public List<Menu> filterMenu(List<Menu> data) {
        Set<String> enabledModes = getFilteredCodes(RequestContext.getTenantId());
        return applyMenuFilters(data, enabledModes);
    }

    private Set<String> getFilteredCodes(String tenantId) {
        BaseConfigQueryParam param = BaseConfigQueryParam.builder().configType(PUBLISH_CONFIG).businessNo(tenantId).build();

        Result<Object> result = null;
        try {
            result = baseConfigApi.getConfigOrDefault(param);
        } catch (Exception e) {
            log.error("调用gs-engine失败", e);
            BizAssert.throwBizException(e, "C0005", ArrayUtils.EMPTY_OBJECT_ARRAY, "调用gs-engine获取配置异常");
        }
        Set<String> enabledModes = new HashSet<>();

        if (result.isSuccess() && result.getData() != null) {
            String resultJson = JsonUtils.toJsonString(result.getData());
            PublishConfig publishConfig = JsonUtils.parseObject(resultJson, PublishConfig.class);
            if (!publishConfig.getPublishSwitch()) {
                enabledModes.add("publishFilter");
            }
            if (!publishConfig.getAuditSwitch()) {
                enabledModes.add("auditFilter");
            }
        }
        return enabledModes;
    }

    public List<Permission> applyResourceFilters(List<Permission> permissions, Set<String> enabledModes) {
        if (CollectionUtils.isEmpty(enabledModes)) {
            return permissions;
        }
        Set<String> allFilteredCodes = getAllFilteredCodes(enabledModes);
        // 应用过滤
        return permissions.stream()
                .filter(permission -> !allFilteredCodes.contains(permission.getPermissionCode()))
                .collect(Collectors.toList());
    }

    public List<Menu> applyMenuFilters(List<Menu> permissions, Set<String> enabledModes) {
        if (CollectionUtils.isEmpty(enabledModes)) {
            return permissions;
        }
        Set<String> allFilteredCodes = getAllFilteredCodes(enabledModes);
        return permissions.stream()
                .filter(permission -> !allFilteredCodes.contains(permission.getUrlAddress()))
                .collect(Collectors.toList());
    }

    private Set<String> getAllFilteredCodes(Set<String> enabledModes) {
        // 获取所有启用的模式的过滤代码
        Set<String> allFilteredCodes = enabledModes.stream()
                .map(mode -> ksGlobalConfig.getFilterModes().get(mode))
                .filter(Objects::nonNull)
                .flatMap(config -> config.getFilteredCodes().stream())
                .collect(Collectors.toSet());
        return allFilteredCodes;
    }
}

