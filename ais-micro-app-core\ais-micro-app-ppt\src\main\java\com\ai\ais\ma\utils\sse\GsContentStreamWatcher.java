package com.ai.ais.ma.utils.sse;

import com.ai.ais.ma.common.sse.watcher.AbstractSseWatcher;
import com.ai.ais.ma.common.sse.watcher.gs.GsMsgData;
import com.ai.ais.ma.common.utils.JsonUtils;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.sse.EventSource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.concurrent.CountDownLatch;

/**
 * 正文内容生成流式处理器
 * 负责处理正文生成的SSE流式响应，逐行解析JSON对象并发送给前端
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-18
 */
@Slf4j
public class GsContentStreamWatcher extends AbstractSseWatcher {

    @Getter
    @Setter
    protected SseEmitter sseEmitter;

    @Getter
    /**
     * 存储未完成的行缓冲区
     */
    protected StringBuffer lineBuffer = new StringBuffer();

    public GsContentStreamWatcher(CountDownLatch latch) {
        super(latch);
    }

    @Override
    public void onEvent(EventSource eventSource, String id, String type, String data) {
        try {
            log.debug("接收到正文生成消息内容：{}", data);
            if (StringUtils.isNotBlank(data)) {
                // 解析消息内容
                String textContent = extractTextFromData(data);
                if (StringUtils.isNotBlank(textContent)) {
                    // 将新内容添加到缓冲区
                    lineBuffer.append(textContent);
                    
                    // 处理缓冲区中的完整行
                    processCompleteLines();
                }
            }
        } catch (IOException e) {
            // 大部分情况是用户主动取消发送,前端关闭连接后,这里会捕获到IO异常
            clientAbort = true;
            throw new BizException(e, "AK001", "用户主动关闭管道");
        } catch (Exception e) {
            clientAbort = true;
            log.error("正文流式输出异常", e);
            throw new BizException(e, "AK002", "正文流式输出异常");
        }
    }

    @Override
    public void onClosed(EventSource eventSource) {
        try {
            // 流结束时处理缓冲区中剩余的内容
            if (sseEmitter != null && !clientAbort) {
                String remainingContent = lineBuffer.toString().trim();
                if (StringUtils.isNotBlank(remainingContent)) {
                    processJsonLine(remainingContent);
                }
                
                // 发送完成信号
                ContentStreamResponse completeResponse = ContentStreamResponse.builder()
                        .type("content_complete")
                        .isComplete(true)
                        .build();
                
                SseEmitter.SseEventBuilder sendDataBuilder = SseEmitter.event()
                        .name("content_complete")
                        .data(JsonUtils.toJsonString(completeResponse));
                sseEmitter.send(sendDataBuilder);
            }
        } catch (Exception e) {
            log.error("发送最终正文内容异常", e);
        }
        super.onClosed(eventSource);
    }

    /**
     * 从SSE数据中提取文本内容
     */
    private String extractTextFromData(String data) {
        if (StringUtils.isBlank(data)) {
            return "";
        }
        
        try {
            if (data.trim().startsWith("{")) {
                GsMsgData gsMsgData = JsonUtils.parseObject(data, GsMsgData.class);
                if (gsMsgData != null && gsMsgData.getText() != null) {
                    return gsMsgData.getText();
                }
            } else {
                // 如果不是JSON格式，直接返回原始数据
                return data;
            }
        } catch (Exception e) {
            log.debug("解析消息内容失败，使用原始数据: {}", data);
            return data;
        }
        
        return "";
    }

    /**
     * 处理缓冲区中的完整行
     */
    private void processCompleteLines() throws IOException {
        String bufferContent = lineBuffer.toString();
        
        // 检查是否包含换行符
        while (bufferContent.contains("\n")) {
            int newlineIndex = bufferContent.indexOf("\n");
            String line = bufferContent.substring(0, newlineIndex).trim();
            
            // 更新缓冲区，移除已处理的行
            bufferContent = bufferContent.substring(newlineIndex + 1);
            lineBuffer = new StringBuffer(bufferContent);
            
            // 处理完整的行
            if (StringUtils.isNotBlank(line)) {
                processJsonLine(line);
            }
        }
    }

    /**
     * 处理单行JSON内容
     */
    private void processJsonLine(String line) throws IOException {
        if (StringUtils.isBlank(line)) {
            return;
        }
        
        try {
            // 尝试解析JSON以验证其有效性
            JsonNode jsonNode = JsonUtils.parseTree(line);
            if (jsonNode != null) {
                log.debug("解析到完整的JSON对象: {}", line);
                
                // 构建返回给前端的数据
                ContentStreamResponse response = ContentStreamResponse.builder()
                        .type("content_update")
                        .jsonContent(line)
                        .parsedJson(jsonNode)
                        .isComplete(false)
                        .build();
                
                // 发送给前端
                if (sseEmitter != null) {
                    SseEmitter.SseEventBuilder sendDataBuilder = SseEmitter.event()
                            .name("content_stream")
                            .data(JsonUtils.toJsonString(response));
                    sseEmitter.send(sendDataBuilder);
                }
            }
        } catch (Exception e) {
            log.warn("解析JSON失败，可能是不完整的行: {}", line, e);
            // 对于无效的JSON，我们可以选择忽略或者发送错误信息
            // 这里选择忽略，因为可能是流式传输中的不完整数据
        }
    }

    /**
     * 正文流式响应数据结构
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class ContentStreamResponse {
        private String type;
        private String jsonContent;
        private JsonNode parsedJson;
        private boolean isComplete;
    }
}
