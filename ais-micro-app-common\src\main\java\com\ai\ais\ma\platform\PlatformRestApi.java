package com.ai.ais.ma.platform;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年04月10日
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface PlatformRestApi {


    /**
     * 接口名称
     */
    String name();

    /**
     * 分组名称
     */
    String groupName();

    /**
     * 角色模板，英文逗号分隔
     */
    String roleTemplateName() default "";

    /**
     * 菜单名称
     */
    String menuName() default "";

    /**
     * 菜单地址
     */
    String menuUrl() default "";

    /**
     * 父级菜单名称
     */
    String parentMenuName() default "";
}
