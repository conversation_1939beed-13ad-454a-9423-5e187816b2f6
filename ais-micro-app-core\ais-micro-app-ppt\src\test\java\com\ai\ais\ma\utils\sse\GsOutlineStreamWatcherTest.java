package com.ai.ais.ma.utils.sse;

import com.ai.ais.ma.common.utils.JsonUtils;
import com.ai.ais.ma.model.vo.KsReportOutline;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

public class GsOutlineStreamWatcherTest {
    /**
     * 测试parseMarkdownPPT方法，验证md大纲解析正确性
     */
    @Test
    public void testParseMarkdownPPT() throws Exception {
        // 构造测试markdown
        String md = "# 公积金办理指南## 第一章 公积金的基本概念###1.1 公积金的定义与作用- 公积金的定义- 公积金的作用与意义- 公积金的缴纳主体###1.2 公积金的种类-住房公积金- 补充公积金- 其他类型公积金###1.3 公积金的缴纳比例与基数- 缴纳比例的确定- 缴纳基数的计算- 缴纳基数的调整## 第二章 公积金办理流程###2.1 公积金办理的基本条件- 缴存单位的要求- 缴存时间的要求- 缴存金额的要求###2.2 公积金办理所需材料- 身份证明材料- 劳动合同或聘用合同- 其他相关材料###2.3 公积金办理的具体步骤-申请公积金账户- 提交相关材料- 审核与批准- 开立公积金账户## 第三章 公积金办理的注意事项###3.1 办理公积金的注意事项- 及时办理公积金开户- 按时足额缴存公积金- 注意公积金账户信息变更###3.2 公积金使用的注意事项- 公积金提取的条件与流程- 公积金贷款的申请与审批- 公积金账户的管理与维护## 第四章 公积金办理的常见问题解答###4.1 公积金办理中的常见问题- 如何查询公积金账户信息？\n" +
                "- 公积金断缴后如何补缴？\n" +
                "- 公积金账户转移如何操作？\n" +
                "\n" +
                "###4.2 公积金使用中的常见问题- 公积金提取的条件有哪些？\n" +
                "- 公积金贷款的利率是多少？\n" +
                "- 公积金账户余额如何查询？\n" +
                "\n" +
                "###4.3 公积金政策的调整与变化- 公积金政策的变化趋势- 公积金政策调整的影响- 如何应对公积金政策的变化";

        List<KsReportOutline> outline = parseMarkdownPPT(md);

        String jsonString = JsonUtils.toJsonString(outline);
        System.out.println(jsonString);

    }

    @Test
    public void testParseMarkdown() throws Exception {
        String s="\n";
        System.out.println(StringUtils.isEmpty(s));
        System.out.println(StringUtils.isBlank(s));
    }

    private List<KsReportOutline> parseMarkdownPPT(String markdown) {
        if (markdown == null || markdown.isEmpty()) {
            return new ArrayList<>();
        }

        String[] lines = markdown.split("\n");
        List<KsReportOutline> outline = new ArrayList<>();
        KsReportOutline currentLevel1 = null;
        KsReportOutline currentLevel2 = null;
        KsReportOutline currentLevel3 = null;

        for (String line : lines) {
            line = line.trim();
            if (StringUtils.isBlank(line)) {
                continue;
            }

            if (line.startsWith("# ")) {
                currentLevel1 = KsReportOutline.builder()
                        .title(line.substring(2).trim())
                        .children(new ArrayList<>())
                        .build();
                outline.add(currentLevel1);
                currentLevel2 = null;
                currentLevel3 = null;
            } else if (line.startsWith("## ")) {
                currentLevel2 = KsReportOutline.builder()
                        .title(line.substring(3).trim())
                        .children(new ArrayList<>())
                        .build();
                if (currentLevel1 != null && currentLevel1.getChildren() != null) {
                    currentLevel1.getChildren().add(currentLevel2);
                }
                currentLevel3 = null;
            } else if (line.startsWith("### ")) {
                currentLevel3 = KsReportOutline.builder()
                        .title(line.substring(4).trim())
                        .children(new ArrayList<>())
                        .build();
                if (currentLevel2 != null && currentLevel2.getChildren() != null) {
                    currentLevel2.getChildren().add(currentLevel3);
                }
            } else if (line.startsWith("- ")) {
                if (currentLevel3 != null) {
                    KsReportOutline content = KsReportOutline.builder()
                            .title(line.substring(2).trim())
                            .children(new ArrayList<>())
                            .build();
                    currentLevel3.getChildren().add(content);
                }
            }
        }

        return outline;
    }
} 