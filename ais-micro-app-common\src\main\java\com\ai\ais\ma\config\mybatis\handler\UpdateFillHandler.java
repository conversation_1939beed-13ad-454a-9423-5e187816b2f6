/*
 * Copyright (c) 2011-2022, baomidou .
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.ai.ais.ma.config.mybatis.handler;

/**
 * 自动更新设置公共字段
 *
 * <AUTHOR>
 */
public interface UpdateFillHandler {

    /**
     * 根据表名判断是否忽略自动设置公共字段
     * <p>
     * 默认都要进行解析并拼自动设置公共字段值
     *
     * @param tableName 表名
     * @return 是否忽略, true:表示忽略，false:需要解析并拼接多租户条件
     */
    default boolean ignoreTable(String tableName) {
        return false;
    }


}
