package com.ai.ais.ma.convert.dto;


import com.ai.ais.ma.infra.po.KsKnowledgeReportPO;
import com.ai.ais.ma.model.dto.KsKnowledgeReportDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface KsKnowledgeReportDtoConverter {

    KsKnowledgeReportDtoConverter INSTANCE = Mappers.getMapper(KsKnowledgeReportDtoConverter.class);

    KsKnowledgeReportPO convert(KsKnowledgeReportDTO param);

    KsKnowledgeReportDTO convert(KsKnowledgeReportPO param);
}


