CREATE TABLE "KS_KNOWLEDGE_REPORT"
(
    "ID" BIGINT IDENTITY(1,1) NOT NULL,
    "NAME" VARCHAR(256 char) NOT NULL,
 "CODE" VARCHAR(256 char) NOT NULL,
 "APP_CODE" VARCHAR(256 char) NOT NULL,
 "THEME_TEXT" VARCHAR(2048 char) NOT NULL,
 "CONTENT" CLOB NULL,
 "TYPE" VARCHAR(256 char) NOT NULL,
 "OUTLINE_DATA" TEXT NULL,
 "CHOSE" VARCHAR(32767 char) NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "CREATE_ID" VARCHAR(1020 char) DEFAULT '0'
 NOT NULL,
 "CREATE_NAME" VARCHAR(1020 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(1020 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(1020 char) DEFAULT '未知用户'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL
);
ALTER TABLE "KS_KNOWLEDGE_REPORT" ADD CONSTRAINT  NOT CLUSTER  PRIMARY KEY("ID") ;
ALTER TABLE "KS_KNOWLEDGE_REPORT" ADD  CONSTRAINT "CONS134230933" CHECK("YN" >= 0) ENABLE ;
CREATE INDEX "IDX_APP_CODE"
    ON "KS_KNOWLEDGE_REPORT"("APP_CODE","CODE");
ALTER INDEX "IDX_APP_CODE" VISIBLE;
CREATE INDEX "IDX_CODE"
    ON "KS_KNOWLEDGE_REPORT"("CODE");
ALTER INDEX "IDX_CODE" VISIBLE;
COMMENT ON TABLE "KS_KNOWLEDGE_REPORT" IS '知识报告表';
COMMENT ON COLUMN "KS_KNOWLEDGE_REPORT"."APP_CODE" IS '应用Code';
COMMENT ON COLUMN "KS_KNOWLEDGE_REPORT"."CHOSE" IS '智能问答选择项';
COMMENT ON COLUMN "KS_KNOWLEDGE_REPORT"."CODE" IS 'Code';
COMMENT ON COLUMN "KS_KNOWLEDGE_REPORT"."CONTENT" IS '文章内容';
COMMENT ON COLUMN "KS_KNOWLEDGE_REPORT"."CREATE_ID" IS '创建用户ID';
COMMENT ON COLUMN "KS_KNOWLEDGE_REPORT"."CREATE_NAME" IS '创建用户名';
COMMENT ON COLUMN "KS_KNOWLEDGE_REPORT"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "KS_KNOWLEDGE_REPORT"."ID" IS '主键';
COMMENT ON COLUMN "KS_KNOWLEDGE_REPORT"."NAME" IS '名称';
COMMENT ON COLUMN "KS_KNOWLEDGE_REPORT"."OUTLINE_DATA" IS '大纲结构';
COMMENT ON COLUMN "KS_KNOWLEDGE_REPORT"."THEME_TEXT" IS '用户原始主题';
COMMENT ON COLUMN "KS_KNOWLEDGE_REPORT"."TYPE" IS '文章内容格式';
COMMENT ON COLUMN "KS_KNOWLEDGE_REPORT"."UPDATE_ID" IS '最后修改用户ID';
COMMENT ON COLUMN "KS_KNOWLEDGE_REPORT"."UPDATE_NAME" IS '最后修改用户名';
COMMENT ON COLUMN "KS_KNOWLEDGE_REPORT"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "KS_KNOWLEDGE_REPORT"."YN" IS '删除标记,0:未删除,其他表示已删除';
