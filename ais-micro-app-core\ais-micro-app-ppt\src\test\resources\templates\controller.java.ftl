package ${package.Controller};

import ${package.Parent}.base.model.CodeParam;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import ${package.Parent}.common.constants.KsApiConstants;
import ${package.Parent}.model.vo.${entity}CreateParam;
import ${package.Parent}.model.vo.${entity}QueryParam;
import ${package.Parent}.model.vo.${entity}UpdateParam;
import ${package.Parent}.model.vo.${entity}VO;
import ${package.Parent}.service.${entity}AppService;
import com.ai.ais.ma.config.cache.aspect.annotation.DebugLog;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

<#if restControllerStyle>
<#else>
import org.springframework.stereotype.Controller;
</#if>
<#if superControllerClassPackage??>
 import ${superControllerClassPackage};
</#if>


import javax.validation.Valid;


/**
* <p>
* ${table.comment} Controller
* </p>
*
* <AUTHOR>
* @since ${date}
*/
@Api(tags = "${table.comment} Controller")
<#if restControllerStyle>
@RestController
<#else>
@Controller
</#if>
@Slf4j
@RequestMapping("")
<#if kotlin>
class ${table.controllerName}<#if superControllerClass??> : ${superControllerClass}()</#if>
<#else>
    <#if superControllerClass??>
public class ${table.controllerName} extends ${superControllerClass} {
    <#else>
public class ${table.controllerName} {
    </#if>

    @Autowired
    private ${table.serviceName} ${table.serviceName?uncap_first};

    @Operation(summary = "${table.comment}分页列表")
    @DebugLog(operation = "${table.comment}分页列表")
    @PostMapping(KsApiConstants.PAGE_API)
    public  Result<Page<${entity}VO>> page(@Validated @RequestBody ${entity}QueryParam param) {

    return Result.success(${table.serviceName?uncap_first}.pageQuery(param));
    }

    @Operation(summary = "${table.comment}详情")
    @DebugLog(operation = "${table.comment}详情")
    @GetMapping(KsApiConstants.CODE_PATH)
    public  Result<${entity}VO> get(@PathVariable("code")  String code) {
    return Result.success(${table.serviceName?uncap_first}.get(code));
    }

    @Operation(summary = "${table.comment}新增")
    @DebugLog(operation = "${table.comment}新增")
    @PostMapping
    public Result<${entity}VO> add(@Validated @RequestBody ${entity}CreateParam createParam) {
        return Result.success(${table.serviceName?uncap_first}.create(createParam));
    }


    @Operation(summary = "${table.comment}更新")
    @DebugLog(operation = "${table.comment}更新")
    @PutMapping(KsApiConstants.CODE_PATH)
    public Result<Boolean> update(@PathVariable("code") String code, @Validated @RequestBody ${entity}UpdateParam param) {
        return Result.success(${table.serviceName?uncap_first}.update(code, param));
    }



    @Operation(summary = "${table.comment}删除")
    @DebugLog(operation = "${table.comment}删除")
    @PostMapping(KsApiConstants.DELETE_API)
    public Result<Boolean> delete(@Validated @RequestBody CodeParam codes) {
        return Result.success(${table.serviceName?uncap_first}.delete(codes));
    }
}


</#if>
