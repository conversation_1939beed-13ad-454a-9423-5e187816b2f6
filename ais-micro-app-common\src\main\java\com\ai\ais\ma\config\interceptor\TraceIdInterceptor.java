package com.ai.ais.ma.config.interceptor;

import com.ai.ais.ma.config.interceptor.filter.BaseHandlerInterceptor;
import io.opentelemetry.api.trace.Span;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletResponse;

/**
 * trance id 拦截器
 */
@Component
@Slf4j
public class TraceIdInterceptor implements BaseHandlerInterceptor {

    @Override
    public boolean preHandle(ServletRequest request, ServletResponse response) throws Exception {
        try {
            String traceId = Span.current().getSpanContext().getTraceId();
            HttpServletResponse httpServletResponse = (HttpServletResponse) response;
            httpServletResponse.setHeader("X-Trace-Id", traceId);
        } catch (Exception e) {
            //ignore
            log.warn("设置traceId异常", e);
        }
        return true;
    }
}
