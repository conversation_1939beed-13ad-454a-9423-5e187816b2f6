package com.ai.ais.ma.service;

import com.ai.ais.ma.base.service.BaseAppByCodeService;
import com.ai.ais.ma.convert.vo.KsKnowledgeReportVoConverter;
import com.ai.ais.ma.infra.po.KsKnowledgeReportPO;
import com.ai.ais.ma.model.dto.KsKnowledgeReportDTO;
import com.ai.ais.ma.model.vo.*;
import com.ai.ais.ma.model.vo.report.KsReportContentQueryParam;
import com.ai.ais.ma.model.vo.report.KsReportOutlineQueryParam;
import com.ai.ais.ma.repository.KsKnowledgeReportRepository;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;

/**
 * <p>
 * 知识报告表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
public interface KsKnowledgeReportAppService extends BaseAppByCodeService<KsKnowledgeReportRepository, KsKnowledgeReportQueryParam, KsKnowledgeReportVoConverter, KsKnowledgeReportPO, KsKnowledgeReportDTO, KsKnowledgeReportVO, KsKnowledgeReportCreateParam, KsKnowledgeReportUpdateParam> {

    /**
     * 生成大纲
     * @param dto
     * @return
     */
    SseEmitter outline(KsReportOutlineQueryParam dto);


    SseEmitter content(KsReportContentQueryParam param) throws IOException;

    /**
     * 重命名报告
     * @param code
     * @param dto
     */
    void rename(String code, KsKnowledgeRenameParam dto);



}
