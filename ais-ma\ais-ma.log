[ais-ma:************:0000] 2025-06-18 16:44:33.305 INFO 69828 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 16:44:33.389 INFO 69828 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 16:44:33.892 INFO 69828 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 16:44:33.944 INFO 69828 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 16:44:33.945 INFO 69828 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 16:44:33.945 INFO 69828 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 16:44:33.946 INFO 69828 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 16:44:33.946 INFO 69828 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 16:44:33.947 INFO 69828 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 16:44:33.947 INFO 69828 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 16:44:33.947 INFO 69828 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 16:44:34.016 INFO 69828 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 16:44:34.035 INFO 69828 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 16:44:34.037 INFO 69828 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 16:44:34.113 INFO 69828 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 16:44:34.113 INFO 69828 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 16:51:18.022 INFO 57064 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 16:51:18.106 INFO 57064 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 16:51:18.667 INFO 57064 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 16:51:18.718 INFO 57064 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 16:51:18.720 INFO 57064 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 16:51:18.720 INFO 57064 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 16:51:18.721 INFO 57064 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 16:51:18.721 INFO 57064 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 16:51:18.722 INFO 57064 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 16:51:18.722 INFO 57064 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 16:51:18.722 INFO 57064 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 16:51:18.794 INFO 57064 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 16:51:18.812 INFO 57064 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 16:51:18.814 INFO 57064 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 16:51:18.894 INFO 57064 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 16:51:18.894 INFO 57064 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 17:46:22.550 INFO 64824 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 17:46:22.643 INFO 64824 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 17:46:23.166 INFO 64824 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 17:46:23.218 INFO 64824 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 17:46:23.220 INFO 64824 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 17:46:23.220 INFO 64824 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 17:46:23.221 INFO 64824 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 17:46:23.221 INFO 64824 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 17:46:23.222 INFO 64824 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 17:46:23.222 INFO 64824 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 17:46:23.222 INFO 64824 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 17:46:23.290 INFO 64824 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 17:46:23.307 INFO 64824 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 17:46:23.309 INFO 64824 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 17:46:23.391 INFO 64824 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 17:46:23.392 INFO 64824 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 18:04:27.100 INFO 40260 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 18:04:27.186 INFO 40260 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 18:04:27.697 INFO 40260 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 18:04:27.747 INFO 40260 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 18:04:27.749 INFO 40260 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:04:27.749 INFO 40260 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:04:27.750 INFO 40260 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:04:27.751 INFO 40260 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:04:27.751 INFO 40260 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:04:27.751 INFO 40260 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:04:27.751 INFO 40260 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:04:27.817 INFO 40260 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 18:04:27.835 INFO 40260 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 18:04:27.837 INFO 40260 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 18:04:27.904 INFO 40260 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 18:04:27.904 INFO 40260 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 18:10:23.094 INFO 48324 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 18:10:23.184 INFO 48324 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 18:10:23.706 INFO 48324 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 18:10:23.758 INFO 48324 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 18:10:23.760 INFO 48324 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:10:23.760 INFO 48324 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:10:23.761 INFO 48324 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:10:23.762 INFO 48324 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:10:23.762 INFO 48324 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:10:23.763 INFO 48324 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:10:23.763 INFO 48324 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:10:23.845 INFO 48324 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 18:10:23.873 INFO 48324 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 18:10:23.876 INFO 48324 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 18:10:23.951 INFO 48324 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 18:10:23.951 INFO 48324 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 18:16:50.133 INFO 61548 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 18:16:50.222 INFO 61548 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 18:16:50.872 INFO 61548 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 18:16:50.932 INFO 61548 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 18:16:50.934 INFO 61548 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:16:50.935 INFO 61548 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:16:50.936 INFO 61548 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:16:50.936 INFO 61548 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:16:50.937 INFO 61548 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:16:50.937 INFO 61548 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:16:50.937 INFO 61548 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:16:51.015 INFO 61548 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 18:16:51.034 INFO 61548 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 18:16:51.037 INFO 61548 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 18:16:51.112 INFO 61548 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 18:16:51.112 INFO 61548 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 18:21:54.315 INFO 74572 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 18:21:54.404 INFO 74572 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 18:21:54.920 INFO 74572 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 18:21:54.971 INFO 74572 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 18:21:54.974 INFO 74572 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:21:54.974 INFO 74572 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:21:54.975 INFO 74572 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:21:54.976 INFO 74572 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:21:54.976 INFO 74572 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:21:54.976 INFO 74572 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:21:54.976 INFO 74572 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:21:55.041 INFO 74572 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 18:21:55.059 INFO 74572 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 18:21:55.061 INFO 74572 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 18:21:55.128 INFO 74572 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 18:21:55.128 INFO 74572 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 18:22:45.102 INFO 67368 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 18:22:45.185 INFO 67368 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 18:22:45.700 INFO 67368 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 18:22:45.751 INFO 67368 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 18:22:45.753 INFO 67368 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:22:45.753 INFO 67368 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:22:45.755 INFO 67368 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:22:45.756 INFO 67368 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:22:45.756 INFO 67368 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:22:45.756 INFO 67368 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:22:45.756 INFO 67368 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:22:45.825 INFO 67368 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 18:22:45.843 INFO 67368 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 18:22:45.846 INFO 67368 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 18:22:45.917 INFO 67368 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 18:22:45.917 INFO 67368 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 18:44:37.768 INFO 35152 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 18:44:37.853 INFO 35152 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 18:44:38.373 INFO 35152 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 18:44:38.424 INFO 35152 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 18:44:38.426 INFO 35152 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:44:38.426 INFO 35152 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:44:38.427 INFO 35152 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:44:38.428 INFO 35152 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:44:38.428 INFO 35152 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:44:38.428 INFO 35152 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:44:38.428 INFO 35152 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:44:38.498 INFO 35152 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 18:44:38.515 INFO 35152 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 18:44:38.517 INFO 35152 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 18:44:38.583 INFO 35152 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 18:44:38.583 INFO 35152 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 18:49:46.647 INFO 55420 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 18:49:46.731 INFO 55420 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 18:49:47.316 INFO 55420 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 18:49:47.385 INFO 55420 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 18:49:47.388 INFO 55420 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:49:47.389 INFO 55420 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:49:47.390 INFO 55420 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:49:47.391 INFO 55420 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:49:47.392 INFO 55420 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:49:47.392 INFO 55420 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:49:47.392 INFO 55420 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 18:49:47.482 INFO 55420 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 18:49:47.513 INFO 55420 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 18:49:47.516 INFO 55420 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 18:49:47.605 INFO 55420 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 18:49:47.606 INFO 55420 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
