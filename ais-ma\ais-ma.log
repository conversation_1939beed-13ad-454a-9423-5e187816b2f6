[ais-ma:************:0000] 2025-06-18 10:36:50.582 INFO 52096 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 10:36:50.665 INFO 52096 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 10:36:51.208 INFO 52096 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 10:36:51.260 INFO 52096 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 10:36:51.262 INFO 52096 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:36:51.263 INFO 52096 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:36:51.264 INFO 52096 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:36:51.264 INFO 52096 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:36:51.264 INFO 52096 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:36:51.265 INFO 52096 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:36:51.265 INFO 52096 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:36:51.336 INFO 52096 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 10:36:51.354 INFO 52096 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 10:36:51.356 INFO 52096 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 10:36:51.433 INFO 52096 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 10:36:51.434 INFO 52096 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 10:36:53.212 ERROR 52096 trace_id=[][] [main] o.s.boot.SpringApplication                Application run failed

org.springframework.boot.context.config.InvalidConfigDataPropertyException: Property 'spring.profiles.active[0]' imported from location 'class path resource [application-localyitiji.yaml]' is invalid in a profile specific resource [origin: class path resource [application-localyitiji.yaml] - 7:9]
	at org.springframework.boot.context.config.InvalidConfigDataPropertyException.lambda$throwOrWarn$1(InvalidConfigDataPropertyException.java:125) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.lang.Iterable.forEach(Iterable.java:75) ~[na:1.8.0_452]
	at java.util.Collections$UnmodifiableCollection.forEach(Collections.java:1082) ~[na:1.8.0_452]
	at org.springframework.boot.context.config.InvalidConfigDataPropertyException.throwOrWarn(InvalidConfigDataPropertyException.java:122) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.context.config.ConfigDataEnvironment.checkForInvalidProperties(ConfigDataEnvironment.java:362) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.context.config.ConfigDataEnvironment.applyToEnvironment(ConfigDataEnvironment.java:326) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.context.config.ConfigDataEnvironment.processAndApply(ConfigDataEnvironment.java:233) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.context.config.ConfigDataEnvironmentPostProcessor.postProcessEnvironment(ConfigDataEnvironmentPostProcessor.java:102) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.context.config.ConfigDataEnvironmentPostProcessor.postProcessEnvironment(ConfigDataEnvironmentPostProcessor.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.env.EnvironmentPostProcessorApplicationListener.onApplicationEnvironmentPreparedEvent(EnvironmentPostProcessorApplicationListener.java:102) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.env.EnvironmentPostProcessorApplicationListener.onApplicationEvent(EnvironmentPostProcessorApplicationListener.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.37.jar:5.3.37]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.37.jar:5.3.37]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.37.jar:5.3.37]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:133) ~[spring-context-5.3.37.jar:5.3.37]
	at org.springframework.boot.context.event.EventPublishingRunListener.environmentPrepared(EventPublishingRunListener.java:85) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$environmentPrepared$2(SpringApplicationRunListeners.java:66) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1259) ~[na:1.8.0_452]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:114) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.environmentPrepared(SpringApplicationRunListeners.java:65) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.prepareEnvironment(SpringApplication.java:344) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:302) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.ai.ais.ma.Starter.main(Starter.java:27) [classes/:na]

[ais-ma:************:0000] 2025-06-18 10:36:53.213 WARN 52096 trace_id=[][] [Thread-8] c.a.n.common.http.HttpClientBeanHolder    [HttpClientBeanHolder] Start destroying common HttpClient
[ais-ma:************:0000] 2025-06-18 10:36:53.214 WARN 52096 trace_id=[][] [Thread-8] c.a.n.common.http.HttpClientBeanHolder    [HttpClientBeanHolder] Destruction of the end
[ais-ma:************:0000] 2025-06-18 10:39:18.332 INFO 55120 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 10:39:18.416 INFO 55120 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 10:39:18.939 INFO 55120 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 10:39:18.992 INFO 55120 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 10:39:18.994 INFO 55120 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:39:18.994 INFO 55120 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:39:18.995 INFO 55120 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:39:18.996 INFO 55120 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:39:18.996 INFO 55120 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:39:18.996 INFO 55120 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:39:18.996 INFO 55120 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:39:19.084 INFO 55120 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 10:39:19.108 INFO 55120 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 10:39:19.112 INFO 55120 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 10:39:19.188 INFO 55120 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 10:39:19.188 INFO 55120 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 10:42:34.929 INFO 56156 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 10:42:35.012 INFO 56156 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 10:42:35.624 INFO 56156 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 10:42:35.693 INFO 56156 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 10:42:35.695 INFO 56156 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:42:35.696 INFO 56156 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:42:35.697 INFO 56156 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:42:35.698 INFO 56156 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:42:35.698 INFO 56156 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:42:35.698 INFO 56156 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:42:35.698 INFO 56156 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:42:35.781 INFO 56156 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 10:42:35.801 INFO 56156 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 10:42:35.804 INFO 56156 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 10:42:35.880 INFO 56156 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 10:42:35.881 INFO 56156 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 10:43:23.416 INFO 45528 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 10:43:23.523 INFO 45528 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 10:43:24.051 INFO 45528 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 10:43:24.109 INFO 45528 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 10:43:24.112 INFO 45528 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:43:24.112 INFO 45528 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:43:24.113 INFO 45528 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:43:24.114 INFO 45528 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:43:24.114 INFO 45528 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:43:24.114 INFO 45528 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:43:24.114 INFO 45528 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:43:24.189 INFO 45528 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 10:43:24.212 INFO 45528 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 10:43:24.215 INFO 45528 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 10:43:24.306 INFO 45528 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 10:43:24.306 INFO 45528 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 10:47:09.272 INFO 11412 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 10:47:09.374 INFO 11412 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 10:47:10.007 INFO 11412 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 10:47:10.066 INFO 11412 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 10:47:10.068 INFO 11412 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:47:10.068 INFO 11412 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:47:10.069 INFO 11412 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:47:10.070 INFO 11412 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:47:10.070 INFO 11412 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:47:10.070 INFO 11412 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:47:10.070 INFO 11412 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:47:10.148 INFO 11412 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 10:47:10.167 INFO 11412 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 10:47:10.169 INFO 11412 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 10:47:10.243 INFO 11412 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 10:47:10.243 INFO 11412 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 10:53:24.160 INFO 59268 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 10:53:24.242 INFO 59268 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 10:53:24.752 INFO 59268 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 10:53:24.803 INFO 59268 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 10:53:24.805 INFO 59268 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:53:24.806 INFO 59268 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:53:24.807 INFO 59268 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:53:24.807 INFO 59268 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:53:24.808 INFO 59268 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:53:24.808 INFO 59268 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:53:24.808 INFO 59268 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:53:24.869 INFO 59268 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 10:53:24.887 INFO 59268 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 10:53:24.889 INFO 59268 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 10:53:24.954 INFO 59268 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 10:53:24.954 INFO 59268 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 10:54:05.337 INFO 48700 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 10:54:05.422 INFO 48700 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 10:54:05.952 INFO 48700 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 10:54:06.007 INFO 48700 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 10:54:06.010 INFO 48700 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:54:06.010 INFO 48700 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:54:06.010 INFO 48700 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:54:06.011 INFO 48700 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:54:06.011 INFO 48700 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:54:06.012 INFO 48700 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:54:06.012 INFO 48700 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:54:06.086 INFO 48700 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 10:54:06.109 INFO 48700 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 10:54:06.111 INFO 48700 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 10:54:06.182 INFO 48700 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 10:54:06.182 INFO 48700 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 10:55:24.059 INFO 24028 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 10:55:24.134 INFO 24028 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 10:55:24.588 INFO 24028 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 10:55:24.631 INFO 24028 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 10:55:24.633 INFO 24028 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:55:24.633 INFO 24028 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:55:24.634 INFO 24028 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:55:24.635 INFO 24028 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:55:24.635 INFO 24028 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:55:24.635 INFO 24028 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:55:24.635 INFO 24028 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:55:24.695 INFO 24028 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 10:55:24.711 INFO 24028 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 10:55:24.713 INFO 24028 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 10:55:24.767 INFO 24028 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 10:55:24.767 INFO 24028 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 10:56:38.161 INFO 52356 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 10:56:38.241 INFO 52356 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 10:56:38.782 INFO 52356 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 10:56:38.837 INFO 52356 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 10:56:38.839 INFO 52356 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:56:38.839 INFO 52356 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:56:38.840 INFO 52356 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:56:38.841 INFO 52356 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:56:38.841 INFO 52356 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:56:38.841 INFO 52356 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:56:38.841 INFO 52356 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:56:38.905 INFO 52356 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 10:56:38.922 INFO 52356 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 10:56:38.924 INFO 52356 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 10:56:38.991 INFO 52356 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 10:56:38.992 INFO 52356 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 10:59:28.107 INFO 17124 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 10:59:28.190 INFO 17124 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 10:59:28.727 INFO 17124 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 10:59:28.783 INFO 17124 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 10:59:28.785 INFO 17124 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:59:28.786 INFO 17124 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:59:28.787 INFO 17124 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:59:28.788 INFO 17124 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:59:28.788 INFO 17124 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:59:28.789 INFO 17124 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:59:28.789 INFO 17124 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 10:59:28.856 INFO 17124 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 10:59:28.875 INFO 17124 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 10:59:28.877 INFO 17124 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 10:59:28.951 INFO 17124 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 10:59:28.951 INFO 17124 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 11:02:41.811 INFO 11952 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 11:02:41.893 INFO 11952 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 11:02:42.396 INFO 11952 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 11:02:42.447 INFO 11952 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 11:02:42.449 INFO 11952 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:02:42.449 INFO 11952 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:02:42.450 INFO 11952 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:02:42.451 INFO 11952 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:02:42.451 INFO 11952 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:02:42.451 INFO 11952 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:02:42.451 INFO 11952 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:02:42.516 INFO 11952 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 11:02:42.534 INFO 11952 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 11:02:42.536 INFO 11952 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 11:02:42.610 INFO 11952 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 11:02:42.610 INFO 11952 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 11:04:07.624 INFO 54984 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 11:04:07.707 INFO 54984 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 11:04:08.214 INFO 54984 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 11:04:08.264 INFO 54984 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 11:04:08.266 INFO 54984 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:04:08.266 INFO 54984 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:04:08.267 INFO 54984 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:04:08.268 INFO 54984 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:04:08.268 INFO 54984 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:04:08.268 INFO 54984 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:04:08.268 INFO 54984 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:04:08.330 INFO 54984 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 11:04:08.348 INFO 54984 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 11:04:08.350 INFO 54984 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 11:04:08.416 INFO 54984 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 11:04:08.416 INFO 54984 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 11:05:27.740 INFO 26920 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 11:05:27.833 INFO 26920 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 11:05:28.396 INFO 26920 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 11:05:28.451 INFO 26920 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 11:05:28.453 INFO 26920 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:05:28.453 INFO 26920 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:05:28.454 INFO 26920 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:05:28.455 INFO 26920 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:05:28.455 INFO 26920 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:05:28.455 INFO 26920 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:05:28.455 INFO 26920 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:05:28.527 INFO 26920 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 11:05:28.550 INFO 26920 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 11:05:28.553 INFO 26920 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 11:05:28.632 INFO 26920 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 11:05:28.633 INFO 26920 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 11:05:39.500 INFO 58252 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 11:05:39.583 INFO 58252 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 11:05:40.093 INFO 58252 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 11:05:40.145 INFO 58252 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 11:05:40.148 INFO 58252 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:05:40.148 INFO 58252 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:05:40.149 INFO 58252 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:05:40.150 INFO 58252 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:05:40.150 INFO 58252 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:05:40.150 INFO 58252 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:05:40.150 INFO 58252 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:05:40.214 INFO 58252 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 11:05:40.231 INFO 58252 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 11:05:40.234 INFO 58252 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 11:05:40.299 INFO 58252 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 11:05:40.300 INFO 58252 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 11:07:25.319 INFO 57848 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 11:07:25.402 INFO 57848 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 11:07:25.922 INFO 57848 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 11:07:25.973 INFO 57848 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 11:07:25.975 INFO 57848 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:07:25.975 INFO 57848 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:07:25.976 INFO 57848 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:07:25.977 INFO 57848 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:07:25.978 INFO 57848 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:07:25.978 INFO 57848 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:07:25.978 INFO 57848 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:07:26.046 INFO 57848 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 11:07:26.066 INFO 57848 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 11:07:26.069 INFO 57848 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 11:07:26.145 INFO 57848 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 11:07:26.146 INFO 57848 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 11:08:52.418 INFO 50072 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 11:08:52.502 INFO 50072 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 11:08:53.103 INFO 50072 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 11:08:53.159 INFO 50072 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 11:08:53.161 INFO 50072 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:08:53.162 INFO 50072 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:08:53.164 INFO 50072 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:08:53.165 INFO 50072 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:08:53.165 INFO 50072 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:08:53.165 INFO 50072 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:08:53.165 INFO 50072 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:08:53.236 INFO 50072 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 11:08:53.261 INFO 50072 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 11:08:53.264 INFO 50072 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 11:08:53.336 INFO 50072 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 11:08:53.337 INFO 50072 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 11:13:10.021 INFO 40520 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 11:13:10.102 INFO 40520 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 11:13:10.599 INFO 40520 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 11:13:10.649 INFO 40520 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 11:13:10.651 INFO 40520 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:13:10.651 INFO 40520 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:13:10.652 INFO 40520 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:13:10.653 INFO 40520 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:13:10.653 INFO 40520 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:13:10.653 INFO 40520 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:13:10.653 INFO 40520 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:13:10.716 INFO 40520 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 11:13:10.734 INFO 40520 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 11:13:10.736 INFO 40520 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 11:13:10.803 INFO 40520 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 11:13:10.803 INFO 40520 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 11:15:30.011 INFO 45832 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 11:15:30.092 INFO 45832 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 11:15:30.611 INFO 45832 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 11:15:30.662 INFO 45832 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 11:15:30.664 INFO 45832 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:15:30.665 INFO 45832 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:15:30.666 INFO 45832 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:15:30.667 INFO 45832 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:15:30.667 INFO 45832 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:15:30.667 INFO 45832 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:15:30.667 INFO 45832 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:15:30.736 INFO 45832 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 11:15:30.753 INFO 45832 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 11:15:30.755 INFO 45832 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 11:15:30.822 INFO 45832 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 11:15:30.823 INFO 45832 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 11:29:21.336 INFO 69092 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 11:29:21.421 INFO 69092 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 11:29:21.936 INFO 69092 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 11:29:21.988 INFO 69092 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 11:29:21.990 INFO 69092 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:29:21.990 INFO 69092 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:29:21.991 INFO 69092 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:29:21.995 INFO 69092 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:29:21.995 INFO 69092 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:29:21.995 INFO 69092 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:29:21.995 INFO 69092 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:29:22.064 INFO 69092 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 11:29:22.084 INFO 69092 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 11:29:22.086 INFO 69092 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 11:29:22.156 INFO 69092 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 11:29:22.156 INFO 69092 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 11:32:02.691 INFO 67788 trace_id=[][] [main] c.a.n.client.env.SearchableProperties     properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[ais-ma:************:0000] 2025-06-18 11:32:02.777 INFO 67788 trace_id=[][] [background-preinit] o.h.validator.internal.util.Version       HV000001: Hibernate Validator 6.2.5.Final
[ais-ma:************:0000] 2025-06-18 11:32:03.308 INFO 67788 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ma:************:0000] 2025-06-18 11:32:03.359 INFO 67788 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ma:************:0000] 2025-06-18 11:32:03.361 INFO 67788 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:32:03.361 INFO 67788 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:32:03.362 INFO 67788 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:32:03.363 INFO 67788 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:32:03.363 INFO 67788 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:32:03.363 INFO 67788 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:32:03.363 INFO 67788 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [bootstrap.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ma:************:0000] 2025-06-18 11:32:03.429 INFO 67788 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ma:************:0000] 2025-06-18 11:32:03.446 INFO 67788 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ma:************:0000] 2025-06-18 11:32:03.448 INFO 67788 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ma:************:0000] 2025-06-18 11:32:03.516 INFO 67788 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[ais-ma:************:0000] 2025-06-18 11:32:03.516 INFO 67788 trace_id=[][] [main] c.a.n.p.a.s.c.ClientAuthPluginManager     [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
