package com.ai.ais.ma.base.service;


import com.ai.ais.ma.base.converter.BaseVoConverter;
import com.ai.ais.ma.base.infra.repository.BaseRepository;
import com.ai.ais.ma.base.model.CodeParam;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.PageParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月03日
 */
public interface BaseAppByCodeService<Repository extends BaseRepository<DTO, PO>,
        QUERY extends PageParam,
        COV extends BaseVoConverter<DTO, VO, C, U>, PO,
        DTO, VO, C, U> {

    VO get(String code);

    Page<VO> pageQuery(QUERY queryParam);

    VO create(C createParam);

    boolean update(String code, U updateParam);

    boolean delete(CodeParam codes);

}
