package com.ai.ais.ma.common.sse.watcher.gs;

import com.ai.ais.ma.common.sse.watcher.AbstractSseWatcher;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.sse.EventSource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年02月11日
 */
@Slf4j
public class GsSseProxyWatcher extends AbstractSseWatcher {

    @Getter
    @Setter
    protected SseEmitter sseEmitter;

    public GsSseProxyWatcher(CountDownLatch latch) {
        super(latch);
    }

    @Override
    public void onEvent(EventSource eventSource, String id, String type, String data) {
        try {
            log.debug("接收到消息内容：{}", data);
            if (StringUtils.isNotBlank(data)) {
                SseEmitter.SseEventBuilder sendDataBuilder = SseEmitter.event()
                        .id(id)
                        .name(type)
                        .data(data);
                sseEmitter.send(sendDataBuilder);
            }
        } catch (IOException e) {
            // 大部分情况是用户主动取消发送,前端关闭连接后,这里会捕获到IO异常
            clientAbort = true;
            throw new BizException(e, "AK001", "用户主动关闭管道");
        } catch (Exception e) {
            clientAbort = true;
            throw new BizException(e, "AK002", "流式输出异常");
        }
    }

}
