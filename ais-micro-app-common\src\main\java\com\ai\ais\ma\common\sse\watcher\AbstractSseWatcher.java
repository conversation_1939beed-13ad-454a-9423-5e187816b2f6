package com.ai.ais.ma.common.sse.watcher;

import com.ai.ais.ma.common.context.RequestContext;
import com.ai.ais.ma.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.vo.prompt.PromptRequest;
import com.chinatelelcom.gs.engine.sdk.common.constants.HeaderKeys;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.chinatelelcom.gs.engine.sdk.common.utils.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.internal.sse.RealEventSource;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import org.apache.commons.lang3.StringUtils;

import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年02月11日
 */
@Slf4j
public abstract class AbstractSseWatcher extends EventSourceListener {

    protected final static MediaType MEDIA_TYPE_JSON = MediaType.parse("application/json; charset=utf-8");

    protected final CountDownLatch latch;

    /**
     * sse管道输出是否已经关闭
     */
    protected volatile boolean clientAbort = false;

    public AbstractSseWatcher(CountDownLatch latch) {
        this.latch = latch;
    }

    @Override
    public void onOpen(EventSource eventSource, Response response) {
        log.info("========= sse连接开启 =========");
    }

    @Override
    public void onClosed(EventSource eventSource) {
        log.info("========= sse连接关闭 =========");
        resumeLLMEngine();
    }

    @Override
    public void onFailure(EventSource eventSource, Throwable e, Response response) {
        try {
            if (e instanceof BizException) {
                BizException bizException = (BizException) e;
                if ("AK001".equals(bizException.getCode())) {
                    log.warn(bizException.getMessage());
                } else {
                    log.warn("sse业务处理异常", e);
                }
            } else {
                log.error("sse系统处理异常,响应结果：{}", JsonUtils.toJsonString(response), e);
            }
        } finally {
            resumeLLMEngine();
        }
    }


    protected void resumeLLMEngine() {
        latch.countDown();
    }


    public void request(PromptRequest request) {
//        String url = SpringContextUtils.getBean(KsGlobalConfig.class).getRpc().getGsUrl();
        String url = "http://192.168.130.249:31170";
        AppSourceType appSourceType = RequestContext.getAppSourceType();
        Request httpRequest = new Request.Builder()
                .url(url + KmsApis.ROOT_GS + KmsApis.RPC + KmsApis.PROMPT + KmsApis.MODEL)
                .addHeader("Content-Type", "application/json")
                .addHeader(HeaderKeys.USER_ID, RequestContext.getUserId())
                .addHeader(HeaderKeys.TENANT_ID, RequestContext.getTenantId())
                .addHeader(HeaderKeys.APP_SOURCE, appSourceType == null ? StringUtils.EMPTY : appSourceType.name())
                .post(RequestBody.create(JsonUtils.toJsonString(request), MEDIA_TYPE_JSON))
                .build();
        RealEventSource realEventSource = new RealEventSource(httpRequest, this);
        OkHttpClient client = SpringContextUtils.getBean(OkHttpClient.class);
        realEventSource.connect(client);
    }


}
