<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.ai</groupId>
    <packaging>pom</packaging>
    <artifactId>ais-micro-app</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <modules>
        <module>ais-micro-app-common</module>
        <module>ais-micro-app-core</module>
        <module>ais-micro-app-bootstrap</module>
    </modules>


    <properties>
        <ais-micro-app.version>1.0.0-SNAPSHOT</ais-micro-app.version>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <java.version>1.8</java.version>
        <spring-boot.version>2.7.18</spring-boot.version>
        <druid.version>1.1.20</druid.version>
        <mapstruct.version>1.5.3.Final</mapstruct.version>
        <misc-generator.version>1.0.5</misc-generator.version>
        <shardingsphere.version>4.1.1</shardingsphere.version>
        <nacos.version>0.2.7</nacos.version>
        <elasticsearch.version>7.17.23</elasticsearch.version>
        <sftp.version>0.1.55</sftp.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring.cloud-version>2021.0.6</spring.cloud-version>
        <mybatis-plus.version>3.5.7</mybatis-plus.version>
        <!--redisson不能再往上升级，需要和spring boot版本配套-->
        <redisson.version>3.17.7</redisson.version>
        <testable.version>0.7.9</testable.version>
        <jakarta-json.version>2.1.1</jakarta-json.version>
        <jacoco.version>0.8.10</jacoco.version>
        <easyexcel-version>3.3.2</easyexcel-version>
        <poi-tl-version>1.12.2</poi-tl-version>
        <javax.servlet.version>2.5</javax.servlet.version>
        <spring-kafka.version>2.9.13</spring-kafka.version>
        <spring-framework.version>5.3.37</spring-framework.version>
        <awsjavasdk.version>1.12.300</awsjavasdk.version>
        <tomcat.version>9.0.100</tomcat.version>
        <cloud-platform.version>2.7.1</cloud-platform.version>
        <spring-session-data-redis.version>2.7.2</spring-session-data-redis.version>
        <fastjson2.version>2.0.53</fastjson2.version>
        <gs-workflow.version>1.0.0-SNAPSHOT</gs-workflow.version>
        <jackson.version>2.17.2</jackson.version>
        <gs-engine.version>2.0.0-SNAPSHOT</gs-engine.version>
        <retrofit.version>2.11.0</retrofit.version>
    </properties>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
    </parent>


    <distributionManagement>
        <repository>
            <id>central</id>
            <name>ct_aics-release-maven-local</name>
            <url>https://artifact.srdcloud.cn/artifactory/ct_aics-release-maven-local</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>ct_aics-snapshot-maven-local</name>
            <url>https://artifact.srdcloud.cn/artifactory/ct_aics-snapshot-maven-local</url>
        </snapshotRepository>
    </distributionManagement>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.ai</groupId>
                <artifactId>ais-micro-app-common</artifactId>
                <version>${ais-micro-app.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ai</groupId>
                <artifactId>ais-micro-app-core</artifactId>
                <version>${ais-micro-app.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ai</groupId>
                <artifactId>ais-micro-app-ppt</artifactId>
                <version>${ais-micro-app.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ai</groupId>
                <artifactId>ais-micro-app-bootstrap</artifactId>
                <version>${ais-micro-app.version}</version>
            </dependency>


            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>gs-engine-kms-sdk</artifactId>
                <version>${gs-engine.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.14.0</version>
            </dependency>
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>1.2</version>
            </dependency>

            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <scope>test</scope>
                <version>${junit.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>32.0.0-jre</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud-version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.2</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>3.0.0</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>3.0.0</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>1.5.20</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>2.2.8</version>
            </dependency>
            <dependency>
                <groupId>jakarta.validation</groupId>
                <artifactId>jakarta.validation-api</artifactId>
                <version>2.0.2</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.15.1</version>
            </dependency>
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>1.5</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>3.0.7</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-expression</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-expression</artifactId>
                <version>5.3.39</version>
            </dependency>
            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-httpclient</artifactId>
                <version>10.1.0</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>2.14.1</version>
            </dependency>
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-core</artifactId>
                <version>8.2.0</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.29</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-ui</artifactId>
                <version>3.0.3</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-actuator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <!-- nacos -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                <version>2021.0.5.0</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>${spring-kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>1.13.0</version>
            </dependency>
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>8.2.0</version>
            </dependency>
            <!-- okhttp3客户端 -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>4.9.3</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp-sse</artifactId>
                <version>4.9.3</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.squareup.retrofit2</groupId>-->
<!--                <artifactId>retrofit</artifactId>-->
<!--                <version>${retrofit.version}</version>-->
<!--                <exclusions>-->
<!--                    <exclusion>-->
<!--                        <groupId>com.squareup.okhttp3</groupId>-->
<!--                        <artifactId>okhttp</artifactId>-->
<!--                    </exclusion>-->
<!--                </exclusions>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>com.squareup.retrofit2</groupId>-->
<!--                <artifactId>converter-jackson</artifactId>-->
<!--                <version>${retrofit.version}</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>1.2.13</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>1.2.13</version>
            </dependency>

            <dependency>
                <groupId>org.xerial.snappy</groupId>
                <artifactId>snappy-java</artifactId>
                <version>1.1.10.5</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.13.5</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>2.13.5</version>
            </dependency>

            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>3.19.6</version>
            </dependency>

            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>3.4.1</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-core</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>

            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>1.70</version>
            </dependency>
            <dependency>
                <groupId>io.github.classgraph</groupId>
                <artifactId>classgraph</artifactId>
                <version>4.8.112</version>
            </dependency>
            <dependency>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-spring-boot-starter</artifactId>
                <version>3.0.3</version>
            </dependency>

            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>html2pdf</artifactId>
                <version>5.0.5</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-el</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-websocket</artifactId>
                <version>${tomcat.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>cloud-platform-client</artifactId>
                <version>${cloud-platform.version}</version>
            </dependency>

            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>1.32</version>
            </dependency>

            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>1.16.1</version>
            </dependency>

            <dependency>
                <groupId>io.opentelemetry.instrumentation</groupId>
                <artifactId>opentelemetry-logback-mdc-1.0</artifactId>
                <version>1.26.0-alpha</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-crypto</artifactId>
                <version>5.7.14</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>5.3.39</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-http</artifactId>
                <version>4.1.118.Final</version>
            </dependency>
            <dependency>
                <groupId>com.amazon.ion</groupId>
                <artifactId>ion-java</artifactId>
                <version>1.10.5</version>
            </dependency>

            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>3.7.2</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>5.3.39</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>5.4.0</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio</artifactId>
                <version>3.4.0</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-handler</artifactId>
                <version>4.1.118.Final</version>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>2.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
