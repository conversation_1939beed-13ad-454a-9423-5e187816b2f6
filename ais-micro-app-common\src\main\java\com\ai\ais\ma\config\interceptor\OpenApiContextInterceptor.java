package com.ai.ais.ma.config.interceptor;

import com.ai.ais.ma.common.context.RequestContext;
import com.ai.ais.ma.common.context.RequestInfo;
import com.ai.ais.ma.common.utils.InterceptorUtils;
import com.ai.ais.ma.common.utils.UserInfoUtils;
import com.ai.ais.ma.config.interceptor.filter.BaseHandlerInterceptor;
import com.chinatelecom.cloud.platform.client.rpc.UserInfo;
import com.chinatelelcom.gs.engine.sdk.common.constants.HeaderKeys;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月31日
 */
@Component
@Slf4j
public class OpenApiContextInterceptor implements BaseHandlerInterceptor {

    public boolean preHandle(ServletRequest request, ServletResponse response) throws Exception {
        String userId = ((HttpServletRequest) request).getHeader(HeaderKeys.USER_ID);
        if (StringUtils.isBlank(userId)) {
            return InterceptorUtils.writeError((HttpServletResponse) response, "A0023", "未设置x-userid请求头");
        }
        String tenantId = ((HttpServletRequest) request).getHeader(HeaderKeys.TENANT_ID);
        if (StringUtils.isBlank(tenantId)) {
            return InterceptorUtils.writeError((HttpServletResponse) response, "A0024", "未设置x-tenantid请求头");
        }
        UserInfo userInfo = UserInfoUtils.getUserInfo(tenantId, userId);
        if (userInfo == null) {
            return InterceptorUtils.writeError((HttpServletResponse) response, "A0025", "用户信息不存在");
        }

        RequestInfo requestInfo = new RequestInfo();
        RequestContext.set(requestInfo);
        requestInfo.setUserId(userId);
        requestInfo.setTenantId(tenantId);
        requestInfo.setUserName(userInfo.getName());
//        requestInfo.setTeam(UserInfoUtils.getUserTeam());
        InterceptorUtils.setAppCode(((HttpServletRequest) request), requestInfo);
        InterceptorUtils.setSourceTypeByRpc(((HttpServletRequest) request), requestInfo);
        return true;
    }

    /**
     * 完成后调用
     *
     * @param request
     * @param response
     * @param ex
     * @throws Exception
     */
    public void afterCompletion(ServletRequest request, ServletResponse response, Exception ex) throws Exception {
        RequestContext.remove();
    }

}
