package com.ai.ais.ma.controller;

import com.ai.ais.ma.common.constants.KsApiConstants;
import com.ai.ais.ma.common.constants.KsSystemConstants;
import com.ai.ais.ma.common.context.RequestContext;
import com.ai.ais.ma.common.utils.JsonUtils;
import com.ai.ais.ma.config.property.KsGlobalConfig;
import com.ai.ais.ma.model.vo.AuthSwapSessionInfo;
import com.ai.ais.ma.platform.PlatformRestApi;
import com.ai.ais.ma.service.PermissionService;
import com.ai.ais.ma.utils.PlatformResourcesUtils;
import com.chinatelecom.cloud.common.pojo.BaseResult;
import com.chinatelecom.cloud.platform.client.config.PlatformSsoProperties;
import com.chinatelecom.cloud.platform.client.constant.SsoConstant;
import com.chinatelecom.cloud.platform.client.rpc.CorpAppResponse;
import com.chinatelecom.cloud.platform.client.rpc.Menu;
import com.chinatelecom.cloud.platform.client.rpc.Permission;
import com.chinatelecom.cloud.platform.client.rpc.PlatformUser;
import com.chinatelecom.cloud.platform.client.session.SessionAccessToken;
import com.chinatelecom.cloud.platform.client.session.SessionMappingStorage;
import com.chinatelecom.cloud.platform.client.util.*;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;


@Api(tags = "账号资源接口")
@RestController
@Slf4j
@RequestMapping({KsApiConstants.WEB_API + KsApiConstants.PLATFORM})
public class PlatformResourcesController {

    @Resource
    private PlatformSsoProperties platformSsoProperties;

    @Resource
    private PermissionService permissionService;

    @Resource
    private KsGlobalConfig ksGlobalConfig;

    @Resource
    private SessionMappingStorage sessionMappingStorage;

    public static String getLogoutUrl(String redirectUrl, String ppSscct) throws IOException {
        String result = UrlUtil.getAuthUrl() + SsoConstant.LOGOUT_URL + "?" + SsoConstant.REDIRECT_URI + "="
                + URLEncoder.encode(redirectUrl, "utf-8");
        if (StringUtils.isNotBlank(ppSscct)) {
            result = result + "&" + KsSystemConstants.COOKIE_ACCESS_TOKEN + "=" + ppSscct;
        }
        return result;
    }

    @GetMapping("/menu")
    @ResponseBody
    @Operation(summary = "获取菜单信息")
    @PlatformRestApi(name = "获取菜单信息", groupName = "账号资源接口")
    Result<List<Menu>> menu(HttpServletResponse response) {
        PlatformUser user = SsoUtil.get();
        BaseResult<List<Menu>> result = PlatformResourcesUtils.getMenuList(user.getCorpCode(), user.getUserId());
        if (result.getCode() != BaseResult.SUCCESS) {
            if (StringUtils.contains(result.getMsg(), "您未被授权使用应用")) {
                throw new BizException("CA999", ArrayUtils.toArray(platformSsoProperties.getAppCode()), "未被授权使用应用");
            } else {
                throw new BizException("CA001", "查询菜单列表失败");
            }
        }
        result.setData(permissionService.filterMenu(result.getData()));
        return Result.success(result.getData());
    }

    @GetMapping("/resource")
    @ResponseBody
    @Operation(summary = "获取前端资源信息,获取前端资源信息,permissionType 0:接口 2：web资源")
    @PlatformRestApi(name = "获取按钮资源信息", groupName = "账号资源接口")
    Result<List<Permission>> resource(HttpServletResponse response) {
        PlatformUser user = SsoUtil.get();
        BaseResult<List<Permission>> result = AuthUtils.getResourceList(user.getCorpCode(), user.getUserId(), "2");

        if (result.getCode() != BaseResult.SUCCESS) {
            throw new BizException("CA002", "查询按钮列表失败");
        }
        result.setData(permissionService.filterResource(result.getData()));
        return Result.success(result.getData());
    }

    @GetMapping("/user")
    @ResponseBody
    @Operation(summary = "获取用户信息")
    @PlatformRestApi(name = "获取用户信息", groupName = "账号资源接口")
    Result<PlatformUser> user() {
        return Result.success(SsoUtil.get());
    }

    @Operation(summary = "退出登录")
    @GetMapping("/logout")
    @PlatformRestApi(name = "退出登录", groupName = "账号资源接口")
    public void logout(@RequestParam(name = KsSystemConstants.COOKIE_ACCESS_TOKEN, required = false) String ppSscct,
                       HttpServletResponse response, HttpServletRequest request) throws IOException {
        SessionAccessToken sessionAccessToken = AccessTokenUtils.getAccessToken(request);
        if (sessionAccessToken != null) {
            String accessToken = sessionAccessToken.getAccessToken();
            log.info("清楚本地session和cookie，accessToken={}", accessToken);
            sessionMappingStorage.removeSessionByToken(accessToken);
            CookieUtils.removeCookie(SsoConstant.COOKIE_ACCESS_TOKEN, "/", response);
        } else {
            log.info("未获取到当前会话信息，尝试直接重定向退出:{}", ppSscct);
        }

        response.setStatus(HttpStatus.MOVED_TEMPORARILY.value());
        String redirectUrl = getLogoutUrl(platformSsoProperties.getAppUrl(), ppSscct);
        response.sendRedirect(redirectUrl);
    }

    @Operation(summary = "获取授权跳转地址")
    @GetMapping("/address")
    @ResponseBody
    @PlatformRestApi(name = "获取授权跳转地址", groupName = "账号资源接口")
    public Result<String> address() {
        return Result.success(platformSsoProperties.getServerUrl());
    }

    @GetMapping("/swap/session/info")
    @ResponseBody
    @Operation(summary = "根据uuid获取swapSession信息")
    @PlatformRestApi(name = "根据uuid获取swapSession信息", groupName = "账号资源接口")
    Result<AuthSwapSessionInfo> swapSessionInfo(@RequestParam(name = "swapAuthCode", required = false) String swapAuthCode, HttpServletRequest request, HttpServletResponse response) {
        boolean openSwapSession = platformSsoProperties.isOpenSwapSession();
        if (openSwapSession) {
            String resultPpSscct = null;
            SessionAccessToken sessionAccessToken = AccessTokenUtils.getAccessToken(request);
            if (sessionAccessToken == null || sessionAccessToken.isExpired()) {
                //当前token过期，通过swapAuthCode取token
                BaseResult<String> result = AuthUtils.swapSession(swapAuthCode);
                String data = result.getData();
                if (result.ifSuccess() && StringUtils.isNotBlank(data)) {
                    resultPpSscct = data;
                } else {
                    log.warn("交换token失败，result:{}", JsonUtils.toJsonString(result));
                }
            } else {
                String sessionId = request.getHeader(KsSystemConstants.COOKIE_ACCESS_TOKEN);
                if (StringUtils.isBlank(sessionId)) {
                    sessionId = CookieUtils.getCookie(request, KsSystemConstants.COOKIE_ACCESS_TOKEN);
                    if (StringUtils.isBlank(sessionId)) {
                        sessionId = request.getSession().getId();
                    }
                }
                resultPpSscct = sessionId;
            }
            if (StringUtils.isNotBlank(resultPpSscct)) {
                response.addHeader(KsSystemConstants.COOKIE_ACCESS_TOKEN, resultPpSscct);
                Cookie cookie = new Cookie(KsSystemConstants.COOKIE_ACCESS_TOKEN, resultPpSscct);
                response.addCookie(cookie);
            }
        }

        return Result.success(new AuthSwapSessionInfo(openSwapSession));
    }

    @Operation(summary = "获取产品logo相关配置")
    @GetMapping("/logo")
    @ResponseBody
    @PlatformRestApi(name = "获取产品logo相关配置", groupName = "账号资源接口")
    public Result<CorpAppResponse> logo() {
        CorpAppResponse corpAppResponse = new CorpAppResponse();
        try {
            BaseResult<CorpAppResponse> baseResult = AuthUtils.getCurrentCorpApp(RequestContext.getTenantId());
            if (baseResult.getCode() != BaseResult.SUCCESS) {
                throw new BizException("CA009", "查询产品logo配置失败：{}", baseResult.getMsg());
            }
            corpAppResponse = baseResult.getData();
        } catch (Exception e) {
            log.warn("查询云平台logo配置失败", e);
        }
        if (corpAppResponse != null) {
            KsGlobalConfig.SystemConfig systemConfig = ksGlobalConfig.getSystem();
            if (StringUtils.isBlank(corpAppResponse.getCustomLogo())) {
                corpAppResponse.setCustomLogo(systemConfig.getCustomLogo());
            }
            if (StringUtils.isBlank(corpAppResponse.getCustomAppName())) {
                corpAppResponse.setCustomAppName(systemConfig.getCustomAppName());
            }
            if (StringUtils.isBlank(corpAppResponse.getCustomIcon())) {
                corpAppResponse.setCustomIcon(systemConfig.getCustomIcon());
            }
        }
        return Result.success(corpAppResponse);
    }
}
