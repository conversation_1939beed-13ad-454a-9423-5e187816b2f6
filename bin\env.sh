#!/bin/bash

DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )/.." && pwd )"

DATE=$(date +%Y%m%d%H%M)
# 基础路径
BASE_PATH=${DIR}
# 服务名称。同时约定部署服务的 jar 包名字也为它。
SERVER_NAME=ais-micro-app

# heapError 存放路径
HEAP_ERROR_PATH=$BASE_PATH/heapError
# JVM 参数
if [ -z "$JAVA_OPS" ]; then
 JAVA_OPS="-Xms256m -Xmx1024m -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$HEAP_ERROR_PATH"
fi
# 默认的参数
JAVA_BASE_OPS="-XX:+DisableAttachMechanism -javaagent:$SERVER_NAME.jar"
