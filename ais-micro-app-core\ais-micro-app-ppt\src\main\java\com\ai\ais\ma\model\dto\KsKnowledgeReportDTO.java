package com.ai.ais.ma.model.dto;

import com.ai.ais.ma.base.dto.BaseAppDTO;
import com.ai.ais.ma.enums.KsReportType;
import com.ai.ais.ma.model.vo.KsKnwlChose;
import com.ai.ais.ma.model.vo.KsReportOutline;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;


/**
 * <p>
 * 知识报告表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
@Getter
@Setter
@Schema(description = "知识报告表分页查询参数")
public class KsKnowledgeReportDTO extends BaseAppDTO implements Serializable {


    private String name;

    private String themeText;

    @Schema(description = "文章内容")
    private String content;

    @Schema(description = "文章内容格式")
    private KsReportType type;

    @Schema(description = "大纲结构")
    private List<KsReportOutline> outlineData;

    @Schema(description = "智能问答选择项")
    private KsKnwlChose chose;
}
