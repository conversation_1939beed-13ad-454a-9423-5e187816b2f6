package com.ai.ais.ma.config.cache.lock.impl;


import com.ai.ais.ma.config.cache.lock.DistributedLock;
import com.ai.ais.ma.config.cache.lock.ZLock;
import com.ai.ais.ma.config.cache.lock.exception.LockException;
import com.ai.ais.ma.config.property.KsGlobalConfig;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * redisson分布式锁实现，基本锁功能的抽象实现
 * 本接口能满足绝大部分的需求，高级的锁功能，请自行扩展或直接使用原生api
 */
public class RedissonDistributedLock implements DistributedLock {
    @Autowired
    private RedissonClient redisson;


    @Value("${spring.redis.lock.prefix:gs:lock}")
    private String lockKeyPrefix;

    @Resource
    private KsGlobalConfig ksGlobalConfig;

    private ZLock getLock(String key, boolean isFair) {
        RLock lock;
        if (isFair) {
            lock = redisson.getFairLock(StringUtils.join(ksGlobalConfig.getSystem().getEnv(), ":", lockKeyPrefix, ":", key));
        } else {
            lock = redisson.getLock(StringUtils.join(ksGlobalConfig.getSystem().getEnv(), ":", lockKeyPrefix, ":", key));
        }
        return new ZLock(key, lock, this);
    }

    @Override
    public ZLock lock(String key, long leaseTime, TimeUnit unit, boolean isFair) {
        ZLock zLock = getLock(key, isFair);
        RLock lock = (RLock) zLock.getLock();
        lock.lock(leaseTime, unit);
        return zLock;
    }

    @Override
    public ZLock tryLock(String key, long waitTime, long leaseTime, TimeUnit unit, boolean isFair) throws InterruptedException {
        ZLock zLock = getLock(key, isFair);
        RLock lock = (RLock) zLock.getLock();
        if (lock.tryLock(waitTime, leaseTime, unit)) {
            return zLock;
        }
        return null;
    }

    @Override
    public void unlock(ZLock zlock) {
        if (zlock != null) {
            Object lock = zlock.getLock();
            if (lock instanceof RLock) {
                RLock rLock = (RLock) lock;
                if (rLock.isLocked()) {
                    rLock.unlock();
                }
            } else {
                throw new LockException("requires RLock type");
            }
        }
    }
}
