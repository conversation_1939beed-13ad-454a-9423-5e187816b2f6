package com.ai.ais.ma.common.utils;

import com.baomidou.mybatisplus.core.conditions.ISqlSegment;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.chinatelecom.gs.engine.kms.sdk.enums.OrderType;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;

import static com.baomidou.mybatisplus.core.enums.SqlKeyword.*;

public class SortUtils {

    public static void buildOrderBy(QueryWrapper<?> queryWrapper, OrderType orderType) {
        if (orderType == null) return;

        switch (orderType) {
            case C_TIME_DESC:
                queryWrapper.orderByDesc("create_time");
                break;
            case C_TIME_ASC:
                queryWrapper.orderByAsc("create_time");
                break;
            case U_TIME_DESC:
                queryWrapper.orderByDesc("update_time");
                break;
            case U_TIME_ASC:
                queryWrapper.orderByAsc("update_time");
                break;
            case NAME_DESC:
                queryWrapper.orderByDesc("name");
                break;
            case NAME_ASC:
                queryWrapper.orderByAsc("name");
                break;

        }
    }


    public static <T> void buildOrderBy(LambdaQueryWrapper<T> queryWrapper, OrderType orderType) {
        if (orderType == null) return;
        switch (orderType) {
            case C_TIME_DESC:
                setOrderOne(queryWrapper, "create_time", false);
                break;
            case C_TIME_ASC:
                setOrderOne(queryWrapper, "create_time", true);
                break;
            case U_TIME_DESC:
                setOrderOne(queryWrapper, "update_time", false);
                break;
            case U_TIME_ASC:
                setOrderOne(queryWrapper, "update_time", true);
                break;
            case NAME_DESC:
                setOrderOne(queryWrapper, "name", false);
                break;
            case NAME_ASC:
                setOrderOne(queryWrapper, "name", true);
                break;

        }
    }

    public static <T> void setOrderOne(LambdaQueryWrapper<T> queryWrapper, String column, boolean asc) {
        try {
            ISqlSegment[] sqlSegments = {ORDER_BY, () -> column, asc ? ASC : DESC};
            queryWrapper.getExpression().add(sqlSegments);
        } catch (Exception e) {
            throw new BizException("设置排序出错：" + e.getMessage());
        }
    }
}
