package com.ai.ais.ma.controller;

import com.ai.ais.ma.base.model.CodeParam;
import com.ai.ais.ma.common.constants.KsApiConstants;
import com.ai.ais.ma.config.cache.aspect.annotation.DebugLog;
import com.ai.ais.ma.model.vo.*;
import com.ai.ais.ma.model.vo.report.KsReportContentQueryParam;
import com.ai.ais.ma.model.vo.report.KsReportOutlineQueryParam;
import com.ai.ais.ma.platform.PlatformRestApi;
import com.ai.ais.ma.rpc.service.impl.GsPromptRpcServiceImpl;
import com.ai.ais.ma.service.KsKnowledgeReportAppService;
import com.ai.ais.ma.utils.HtmlXssUtils;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.io.IOException;


/**
 * <p>
 * 知识报告表 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
@Api(tags = "知识报告表 Controller")
@RestController
@Slf4j
@RequestMapping({
        KsApiConstants.WEB_API + KsApiConstants.KNOWLEDGE + KsApiConstants.AI_REPORT,
        KsApiConstants.OPENAPI + KsApiConstants.KNOWLEDGE + KsApiConstants.AI_REPORT,
        KsApiConstants.RPC + KsApiConstants.KNOWLEDGE + KsApiConstants.AI_REPORT})
public class KsKnowledgeReportController {

    @Resource
    private KsKnowledgeReportAppService ksKnowledgeReportAppService;

    @Resource
    private GsPromptRpcServiceImpl gsPromptRpcServiceImpl;

    @Operation(summary = "分页查询AI文章报告")
    @PlatformRestApi(name = "分页查询AI文章报告", groupName = "智能写作")
    @PostMapping(KmsApis.PAGE_API)
    public Result<Page<KsKnowledgeReportVO>> page(@Validated @RequestBody KsKnowledgeReportQueryParam appQueryParam) {
        return Result.success(ksKnowledgeReportAppService.pageQuery(appQueryParam));
    }

    @Operation(summary = "AI文章报告详情")
    @PlatformRestApi(name = "AI文章报告详情", groupName = "智能写作")
    @GetMapping(KmsApis.CODE_PATH)
    public Result<KsKnowledgeReportVO> get(@PathVariable("code") String code) {
        return Result.success(ksKnowledgeReportAppService.get(code));
    }

    @Operation(summary = "删除AI文章报告")
    @PlatformRestApi(name = "删除AI文章报告", groupName = "智能写作")
    @PostMapping(KmsApis.DELETE_API)
    public Result<Boolean> delete(@Validated @RequestBody CodeParam codeParam) {
        return Result.success(ksKnowledgeReportAppService.delete(codeParam));
    }

    @Operation(summary = "新增AI文章报告")
    @PlatformRestApi(name = "新增AI文章报告", groupName = "智能写作")
    @PostMapping
    public Result<KsKnowledgeReportVO> add(@Validated @RequestBody KsKnowledgeReportCreateParam dto) {
        if (StringUtils.isNotBlank(dto.getContent())) {
            dto.setContent(HtmlXssUtils.safe(dto.getContent()));
        }
        return Result.success(ksKnowledgeReportAppService.create(dto));
    }

    @Operation(summary = "更新AI文章报告")
    @PlatformRestApi(name = "更新AI文章报告", groupName = "智能写作")
    @PutMapping(KmsApis.CODE_PATH)
    public Result<Boolean> update(@PathVariable("code") String code, @Validated @RequestBody KsKnowledgeReportUpdateParam dto) {
        if (StringUtils.isNotBlank(dto.getContent())) {
            dto.setContent(HtmlXssUtils.safe(dto.getContent()));
        }
        return Result.success(ksKnowledgeReportAppService.update(code, dto));
    }

    @Operation(summary = "更新AI文章报告名称")
    @PlatformRestApi(name = "更新AI文章报告名称", groupName = "智能写作")
    @PutMapping(KmsApis.CODE_PATH + KmsApis.RENAME)
    public Result<Boolean> rename(@PathVariable("code") String code, @Validated @RequestBody KsKnowledgeRenameParam dto) {
        ksKnowledgeReportAppService.rename(code, dto);
        return Result.success(true);
    }


//    @Deprecated
//    @Operation(summary = "AI报告大纲编写")
//    @PlatformRestApi(name = "AI报告大纲编写", groupName = "智能写作")
//    @DebugLog(operation = "AI报告大纲编写")
//    @PostMapping(KmsApis.AI_REPORT_OUTLINE)
//    public SseEmitter outline(@Validated @RequestBody ReportOutlineQueryParam dto) throws IOException {
//        try {
//            return knowledgeReportService.outlineSse(dto);
//        } catch (Exception e) {
//            log.error("生成大纲异常", e);
//            return gsPromptRpcServiceImpl.errorResponse(null);
//        }
//    }

    @Operation(summary = "AI报告大纲编写")
    @DebugLog(operation = "AI报告大纲编写")
    @PostMapping(KmsApis.AI_REPORT_OUTLINE)
    @PlatformRestApi(name = "大纲编写", groupName = "智能写作")
    public SseEmitter outline(@Validated @RequestBody KsReportOutlineQueryParam dto) {
        return ksKnowledgeReportAppService.outline(dto);
    }

    @Operation(summary = "AI报告正文编写")
    @PlatformRestApi(name = "AI报告正文编写", groupName = "智能写作")
    @PostMapping(path = KmsApis.AI_REPORT_CONTENT, produces = {MediaType.TEXT_EVENT_STREAM_VALUE})
    public SseEmitter content(@Validated @RequestBody KsReportContentQueryParam param) throws IOException {
        try {
            return ksKnowledgeReportAppService.content(param);
        } catch (BizException e) {
            log.error("生成正文异常", e);
            return gsPromptRpcServiceImpl.errorResponse(e.getUserTip());
        } catch (Exception e) {
            log.error("生成正文异常", e);
            return gsPromptRpcServiceImpl.errorResponse(null);
        }
    }


}

