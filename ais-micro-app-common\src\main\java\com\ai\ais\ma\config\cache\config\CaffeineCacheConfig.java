package com.ai.ais.ma.config.cache.config;


import com.ai.ais.ma.config.cache.customized.CustomizedCaffeineCacheManager;
import com.ai.ais.ma.config.cache.lock.impl.JavaDistributedLock;
import org.redisson.spring.starter.RedissonAutoConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * CaffineCacheConfig
 *
 * <AUTHOR>
 * @date 2023-05-29 10:04
 */
@Configuration
@EnableCaching(order = 10)
@ConditionalOnProperty(prefix = "ks.cache", name = "type", havingValue = "CAFFEINE")
@EnableAutoConfiguration(exclude = {RedissonAutoConfiguration.class, RedisAutoConfiguration.class})
public class CaffeineCacheConfig {

    @Bean
    @Primary
    public CustomizedCaffeineCacheManager getCaffeineCacheManager() {
        CustomizedCaffeineCacheManager cacheManager = new CustomizedCaffeineCacheManager();
        cacheManager.setCacheSpecification("maximumSize=1000000,expireAfterAccess=3600s");
        cacheManager.setAllowNullValues(false);
        return cacheManager;
    }

    @Bean
    @Primary
    JavaDistributedLock caffeineCache() {
        return new JavaDistributedLock();
    }

}
