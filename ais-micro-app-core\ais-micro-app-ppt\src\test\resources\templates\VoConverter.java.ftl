package ${package.Parent}.convert.vo;

import ${package.Parent}.base.converter.BaseVoConverter;
import ${package.Parent}.model.dto.${entity}DTO;
import ${package.Parent}.model.vo.${entity}CreateParam;
import ${package.Parent}.model.vo.${entity}UpdateParam;
import ${package.Parent}.model.vo.${entity}VO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ${entity}VoConverter extends BaseVoConverter< ${entity}DTO, ${entity}VO, ${entity}CreateParam, ${entity}UpdateParam> {

${entity}VoConverter INSTANCE = Mappers.getMapper(${entity}VoConverter.class);


}


