package com.ai.ais.ma.platform;

import com.ai.ais.ma.common.utils.InterceptorUtils;
import com.ai.ais.ma.common.utils.JsonUtils;
import com.chinatelecom.cloud.common.pojo.BaseResult;
import com.chinatelecom.cloud.platform.client.config.PlatformSsoProperties;
import com.chinatelecom.cloud.platform.client.filter.AuthFilter;
import com.chinatelecom.cloud.platform.client.rpc.PlatformUser;
import com.chinatelecom.cloud.platform.client.session.SessionAccessToken;
import com.chinatelecom.cloud.platform.client.util.AccessTokenUtils;
import com.chinatelecom.cloud.platform.client.util.AuthUtils;
import com.chinatelecom.cloud.platform.client.util.SsoUtil;
import com.chinatelelcom.gs.engine.sdk.common.utils.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.AntPathMatcher;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年04月10日
 */
@Slf4j
public class PlatformAuthFilter extends AuthFilter {

    List<String> authExcludeUrls = null;
    List<String> authIncludeUrlsList = null;
    private AntPathMatcher ANT_PATH_MATCHER = new AntPathMatcher();
    private String DEFAULT_AUTH_INCLUDE_URLS = "/ais/*/web/**";

    @Override
    public void init(PlatformSsoProperties properties) {
        super.init(properties);
        if (properties.getAuthExcludeUrls() != null && !properties.getAuthExcludeUrls().isEmpty()) {
            authExcludeUrls = Arrays.stream(properties.getAuthExcludeUrls().split(",")).collect(Collectors.toList());
            log.info("接口拦截注册排除地址列表:{}", JsonUtils.toJsonString(authExcludeUrls));
        }
        String authIncludeUrlsConfig = SpringContextUtils.getProperty("platform.client.auth-include-urls");
        if (StringUtils.isNotBlank(authIncludeUrlsConfig)) {
            authIncludeUrlsList = Arrays.stream(authIncludeUrlsConfig.split(",")).collect(Collectors.toList());
        } else {
            authIncludeUrlsList = Arrays.stream(DEFAULT_AUTH_INCLUDE_URLS.split(",")).collect(Collectors.toList());
        }
        log.info("接口拦截注册包含地址列表:{}", JsonUtils.toJsonString(authIncludeUrlsList));
    }

    @Override
    public boolean isAccessAllowed(HttpServletRequest request, HttpServletResponse response) throws IOException {
        if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
            return true;
        }
        if (this.getProperties().isDev() && this.getProperties().isMock() && getMockFlag(request)) {
            return true;
        }
        String requestUri = request.getRequestURI();
        log.info("请求requestUri:{}，请求方式：{}", requestUri, request.getMethod());
        if (!isMatchUrl(requestUri)) {
            log.debug("请求地址不匹配，不进行权限拦截");
            return true;
        }

        PlatformUser platformUser = SsoUtil.get();
        // 超管不鉴权，仅限认证系统
        if (platformUser.getIsSuperAdmin() != null && platformUser.getIsSuperAdmin()) {
            log.debug("超级管理员，不进行权限拦截");
            return true;
        }
        SessionAccessToken sessionAccessToken = AccessTokenUtils.getAccessToken(request);
        BaseResult<Void> authResult = AuthUtils.getAuth(platformUser.getCorpCode(),
                requestUri, request.getMethod(), sessionAccessToken.getAccessToken());
        if (authResult.ifSuccess()) {
            return true;
        } else {
            log.error("接口鉴权失败：{}", authResult.getMsg());
            if (StringUtils.equals(authResult.getMsg(), "token无效")) {
                // 正常逻辑不会走到这里，认证平台有问题导致loginFilter未拦截到，走到这里做个兜底
                log.error("用户登录token失效，退出登录，userId:{}", platformUser.getUserId());
                return InterceptorUtils.writeError(response, "CA999", "登录失效，请重新登录");
            } else {
                return InterceptorUtils.writeError(response, "CA999", authResult.getMsg());
            }
        }
    }

    private boolean isMatchUrl(String requestUri) {
        if (CollectionUtils.isNotEmpty(authExcludeUrls)) {
            for (String excludeUrl : authExcludeUrls) {
                // 兼容历史的全部路径排除
                if (StringUtils.equals(excludeUrl, "/*")) {
                    return false;
                }
                if (ANT_PATH_MATCHER.match(excludeUrl, requestUri)) {
                    return false;
                }
            }
        }

        if (CollectionUtils.isNotEmpty(authIncludeUrlsList)) {
            for (String includeUrl : authIncludeUrlsList) {
                if (ANT_PATH_MATCHER.match(includeUrl, requestUri)) {
                    return true;
                }
            }
        }
        return false;
    }


}
