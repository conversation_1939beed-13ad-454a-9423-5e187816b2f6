package com.ai.ais.ma.model.vo.report;

import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.report.OperateSourceType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @Author: gaoxianjun
 * @CreateTime: 2024-12-16
 * @Description:
 * @Version: 1.0
 */
@Data
public class KsReportOutlineQueryParam {
    @NotEmpty(message = "用户原始主题不能为空")
    @Schema(description = "用户原始主题")
    private String themeText;

    @NotNull(message = "生成类型不能为空")
    @Schema(description = "生成类型")
    private OperateSourceType source;

    @Schema(description = "已生成的大纲")
    private String outline;

    @Schema(description = "语言")
    private String language;
}
