package com.ai.ais.ma.common.utils;

import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.function.Supplier;

/**
 * 业务断言工具, 不符合条件抛出BizException
 *
 * <AUTHOR>
 * @date 2023-04-10 17:26
 */
public class BizAssert {

    /**
     * 断言传入对象不能为空
     *
     * @param object
     * @param code
     * @param errorMsg
     * @param params
     * @param <T>
     * @return
     */
    public static <T> T notNull(T object, String code, String errorMsg, Object... params) {
        return notNull(object, () -> new BizException(code, errorMsg, params));
    }

    /**
     * 断言传入对象不能为空
     *
     * @param object
     * @param errorSupplier
     * @param <T>
     * @param <X>
     * @return
     * @throws X
     */
    public static <T, X extends Throwable> T notNull(T object, Supplier<X> errorSupplier) throws X {
        if (null == object) {
            throw errorSupplier.get();
        }
        return object;
    }


    /**
     * 断言参数必须为空
     *
     * @param object
     * @param code
     * @param errorMsg
     * @param params
     */
    public static void isNull(Object object, String code, String errorMsg, Object... params) {
        isNull(object, () -> new BizException(code, errorMsg, params));
    }

    /**
     * 断言参数必须为空
     *
     * @param object
     * @param errorSupplier
     * @param <X>
     * @throws X
     */
    public static <X extends Throwable> void isNull(Object object, Supplier<X> errorSupplier) throws X {
        if (null != object) {
            throw errorSupplier.get();
        }
    }

    /**
     * 断言字符串不能为空
     *
     * @param object
     * @param code
     * @param errorMsg
     * @param params
     * @param <T>
     * @return
     */
    public static <T extends CharSequence> T notEmpty(T object, String code, String errorMsg, Object... params) {
        return notEmpty(object, () -> new BizException(code, errorMsg, params));
    }

    /**
     * 断言字符串不能为空
     *
     * @param text
     * @param errorSupplier
     * @param <T>
     * @param <X>
     * @return
     * @throws X
     */
    public static <T extends CharSequence, X extends Throwable> T notEmpty(T text, Supplier<X> errorSupplier) throws X {
        if (StringUtils.isEmpty(text)) {
            throw errorSupplier.get();
        }
        return text;
    }

    /**
     * 断言对象必须相等
     *
     * @param obj1
     * @param obj2
     * @param code
     * @param errorMsg
     * @param params
     */
    public static void equals(Object obj1, Object obj2, String code, String errorMsg, Object... params) {
        equals(obj1, obj2, () -> new BizException(code, errorMsg, params));
    }

    /**
     * 断言两个对象必须相等
     *
     * @param obj1
     * @param obj2
     * @param errorSupplier
     * @param <X>
     * @throws X
     */
    public static <X extends Throwable> void equals(Object obj1, Object obj2, Supplier<X> errorSupplier) throws X {
        if (!Objects.equals(obj1, obj2)) {
            throw errorSupplier.get();
        }
    }

    /**
     * 断言对象必须相等
     *
     * @param obj1
     * @param obj2
     * @param code
     * @param errorMsg
     * @param params
     */
    public static void notEquals(Object obj1, Object obj2, String code, String errorMsg, Object... params) {
        notEquals(obj1, obj2, () -> new BizException(code, errorMsg, params));
    }

    /**
     * 断言两个对象必须相等
     *
     * @param obj1
     * @param obj2
     * @param errorSupplier
     * @param <X>
     * @throws X
     */
    public static <X extends Throwable> void notEquals(Object obj1, Object obj2, Supplier<X> errorSupplier) throws X {
        if (Objects.equals(obj1, obj2)) {
            throw errorSupplier.get();
        }
    }

    /**
     * 断言集合不能为空
     *
     * @param collect
     * @param code
     * @param errorMsg
     * @param params
     * @param <T>
     * @return
     */
    public static <T extends Collection> T notEmpty(T collect, String code, String errorMsg, Object... params) {
        return notEmpty(collect, () -> new BizException(code, errorMsg, params));
    }

    /**
     * 断言集合不能为空
     *
     * @param collect
     * @param errorSupplier
     * @param <T>
     * @param <X>
     * @return
     * @throws X
     */
    public static <T extends Collection, X extends Throwable> T notEmpty(T collect, Supplier<X> errorSupplier) throws X {
        if (CollectionUtils.isEmpty(collect)) {
            throw errorSupplier.get();
        }
        return collect;
    }

    /**
     * 断言集合不能为空
     *
     * @param map
     * @param code
     * @param errorMsg
     * @param params
     * @param <T>
     * @return
     */
    public static <T extends Map> T notEmpty(T map, String code, String errorMsg, Object... params) {
        return notEmpty(map, () -> new BizException(code, errorMsg, params));
    }

    /**
     * 断言集合不能为空
     *
     * @param map
     * @param errorSupplier
     * @param <T>
     * @param <X>
     * @return
     * @throws X
     */
    public static <T extends Map, X extends Throwable> T notEmpty(T map, Supplier<X> errorSupplier) throws X {
        if (MapUtils.isEmpty(map)) {
            throw errorSupplier.get();
        }
        return map;
    }

    /**
     * 断言集合为空
     *
     * @param collect
     * @param code
     * @param errorMsg
     * @param params
     * @param <T>
     * @return
     */
    public static <T extends Collection> T isEmpty(T collect, String code, String errorMsg, Object... params) {
        return isEmpty(collect, () -> new BizException(code, errorMsg, params));
    }

    /**
     * 断言集合不能为空
     *
     * @param collect
     * @param errorSupplier
     * @param <T>
     * @param <X>
     * @return
     * @throws X
     */
    public static <T extends Collection, X extends Throwable> T isEmpty(T collect, Supplier<X> errorSupplier) throws X {
        if (CollectionUtils.isNotEmpty(collect)) {
            throw errorSupplier.get();
        }
        return collect;
    }

    /**
     * 断言条件为true
     *
     * @param condition 断言条件
     * @param code      异常编码
     * @param errorMsg  异常信息
     * @param params    异常信息参数
     */
    public static void assertTrue(Boolean condition, String code, String errorMsg, Object... params) {
        if (!condition) {
            throw new BizException(code, errorMsg, params);
        }
    }

    /**
     * 断言两个对象必须相等
     *
     * @param condition
     * @param errorSupplier
     * @param <X>
     * @throws X
     */
    public static <X extends Throwable> void assertTrue(Boolean condition, Supplier<X> errorSupplier) throws X {
        if (!condition) {
            throw errorSupplier.get();
        }
    }

    /**
     * 断言条件为false
     *
     * @param condition 断言条件
     * @param code      异常编码
     * @param errorMsg  异常信息
     * @param params    异常信息参数
     */
    public static void assertFalse(Boolean condition, String code, String errorMsg, Object... params) {
        if (condition) {
            throw new BizException(code, errorMsg, params);
        }
    }

    /**
     * 抛出异常
     *
     * @param code
     * @param errorMsg
     * @param params
     */
    public static void throwBizException(String code, String errorMsg, Object... params) {
        throw new BizException(code, errorMsg, params);
    }

    /**
     * 抛出异常
     *
     * @param code
     * @param userTipParam
     * @param errorMsg
     * @param params
     */
    public static void throwBizException(String code, Object[] userTipParam, String errorMsg, Object... params) {
        throw new BizException(code, userTipParam, errorMsg, params);
    }

    /**
     * 抛出异常
     *
     * @param cause
     * @param code
     * @param userTipParam
     * @param message
     * @param messageParam
     */
    public static void throwBizException(Throwable cause, String code, Object[] userTipParam, String message, Object... messageParam) {
        throw new BizException(cause, code, userTipParam, message, messageParam);
    }
}
