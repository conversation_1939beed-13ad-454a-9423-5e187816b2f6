[ais-ks:************:8066] 2025-06-18 16:44:44.209 WARN 69828 trace_id=[][] [main] c.a.c.n.c.NacosPropertySourceBuilder      Ignore the empty nacos configuration and get it based on dataId[telecom-ai-common.yaml] & group[DEFAULT_GROUP]
[ais-ks:************:8066] 2025-06-18 16:44:44.512 WARN 69828 trace_id=[][] [main] c.a.c.n.c.NacosPropertySourceBuilder      Ignore the empty nacos configuration and get it based on dataId[ais-ma] & group[DEFAULT_GROUP]
[ais-ks:************:8066] 2025-06-18 16:44:44.816 WARN 69828 trace_id=[][] [main] c.a.c.n.c.NacosPropertySourceBuilder      Ignore the empty nacos configuration and get it based on dataId[ais-ma.yaml] & group[DEFAULT_GROUP]
[ais-ks:************:8066] 2025-06-18 16:44:45.118 WARN 69828 trace_id=[][] [main] c.a.c.n.c.NacosPropertySourceBuilder      Ignore the empty nacos configuration and get it based on dataId[ais-ma-localyitiji.yaml] & group[DEFAULT_GROUP]
[ais-ks:************:8066] 2025-06-18 16:44:45.119 INFO 69828 trace_id=[][] [main] b.c.PropertySourceBootstrapConfiguration  Located property source: [BootstrapPropertySource {name='bootstrapProperties-ais-ma-localyitiji.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-ais-ma.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-ais-ma,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-telecom-ai-common.yaml,DEFAULT_GROUP'}]
[ais-ks:************:8066] 2025-06-18 16:44:45.135 INFO 69828 trace_id=[][] [main] com.ai.ais.ma.Starter                     The following 1 profile is active: "localyitiji"
[ais-ks:************:8066] 2025-06-18 16:44:46.610 INFO 69828 trace_id=[][] [main] .s.d.r.c.RepositoryConfigurationDelegate  Multiple Spring Data modules found, entering strict repository configuration mode
[ais-ks:************:8066] 2025-06-18 16:44:46.613 INFO 69828 trace_id=[][] [main] .s.d.r.c.RepositoryConfigurationDelegate  Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[ais-ks:************:8066] 2025-06-18 16:44:46.689 INFO 69828 trace_id=[][] [main] .s.d.r.c.RepositoryConfigurationDelegate  Finished Spring Data repository scanning in 53 ms. Found 0 Redis repository interfaces.
[ais-ks:************:8066] 2025-06-18 16:44:47.068 INFO 69828 trace_id=[][] [main] o.s.cloud.context.scope.GenericScope      BeanFactory id=5e9c01e1-0017-3b84-b949-47df645d3a02
[ais-ks:************:8066] 2025-06-18 16:44:47.144 INFO 69828 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ks:************:8066] 2025-06-18 16:44:47.146 INFO 69828 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrapProperties-ais-ma-localyitiji.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:44:47.146 INFO 69828 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrapProperties-ais-ma.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:44:47.146 INFO 69828 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrapProperties-ais-ma,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:44:47.146 INFO 69828 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrapProperties-telecom-ai-common.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:44:47.147 INFO 69828 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ks:************:8066] 2025-06-18 16:44:47.147 INFO 69828 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:44:47.147 INFO 69828 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:44:47.147 INFO 69828 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:44:47.147 INFO 69828 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:44:47.147 INFO 69828 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:44:47.147 INFO 69828 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:44:47.147 INFO 69828 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:44:47.147 INFO 69828 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [application-localyitiji.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:44:47.147 INFO 69828 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [application.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:44:47.148 INFO 69828 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:44:47.155 INFO 69828 trace_id=[][] [main] trationDelegate$BeanPostProcessorChecker  Bean 'hibernateValidatorConfiguration' of type [com.ai.ais.ma.config.validator.HibernateValidatorConfiguration$$EnhancerBySpringCGLIB$$cf859d2f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[ais-ks:************:8066] 2025-06-18 16:44:47.207 INFO 69828 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ks:************:8066] 2025-06-18 16:44:47.688 INFO 69828 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ks:************:8066] 2025-06-18 16:44:47.689 INFO 69828 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ks:************:8066] 2025-06-18 16:44:48.056 INFO 69828 trace_id=[][] [main] o.s.b.w.embedded.tomcat.TomcatWebServer   Tomcat initialized with port(s): 8066 (http)
[ais-ks:************:8066] 2025-06-18 16:44:48.075 INFO 69828 trace_id=[][] [main] o.a.coyote.http11.Http11NioProtocol       Initializing ProtocolHandler ["http-nio-8066"]
[ais-ks:************:8066] 2025-06-18 16:44:48.078 INFO 69828 trace_id=[][] [main] o.apache.catalina.core.StandardService    Starting service [Tomcat]
[ais-ks:************:8066] 2025-06-18 16:44:48.078 INFO 69828 trace_id=[][] [main] org.apache.catalina.core.StandardEngine   Starting Servlet engine: [Apache Tomcat/9.0.100]
[ais-ks:************:8066] 2025-06-18 16:44:48.105 WARN 69828 trace_id=[][] [main] o.a.c.webresources.DirResourceSet         Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8066.1021757492531978708] which is part of the web application [/ais/ma]
[ais-ks:************:8066] 2025-06-18 16:44:48.306 INFO 69828 trace_id=[][] [main] o.a.c.c.C.[.[localhost].[/ais/ma]         Initializing Spring embedded WebApplicationContext
[ais-ks:************:8066] 2025-06-18 16:44:48.307 INFO 69828 trace_id=[][] [main] w.s.c.ServletWebServerApplicationContext  Root WebApplicationContext: initialization completed in 3141 ms
[ais-ks:************:8066] 2025-06-18 16:44:48.426 INFO 69828 trace_id=[][] [main] c.a.a.m.c.c.c.RedissonConfiguration       init single server
[ais-ks:************:8066] 2025-06-18 16:44:48.810 INFO 69828 trace_id=[][] [main] org.redisson.Version                      Redisson 3.17.7
[ais-ks:************:8066] 2025-06-18 16:44:48.964 WARN 69828 trace_id=[][] [Thread-8] c.a.n.common.http.HttpClientBeanHolder    [HttpClientBeanHolder] Start destroying common HttpClient
[ais-ks:************:8066] 2025-06-18 16:44:48.964 WARN 69828 trace_id=[][] [Thread-19] c.a.nacos.common.notify.NotifyCenter      [NotifyCenter] Start destroying Publisher
[ais-ks:************:8066] 2025-06-18 16:44:48.965 WARN 69828 trace_id=[][] [Thread-19] c.a.nacos.common.notify.NotifyCenter      [NotifyCenter] Destruction of the end
[ais-ks:************:8066] 2025-06-18 16:44:48.965 WARN 69828 trace_id=[][] [Thread-8] c.a.n.common.http.HttpClientBeanHolder    [HttpClientBeanHolder] Destruction of the end
[ais-ks:************:8066] 2025-06-18 16:51:29.035 WARN 57064 trace_id=[][] [main] c.a.c.n.c.NacosPropertySourceBuilder      Ignore the empty nacos configuration and get it based on dataId[telecom-ai-common.yaml] & group[DEFAULT_GROUP]
[ais-ks:************:8066] 2025-06-18 16:51:29.341 WARN 57064 trace_id=[][] [main] c.a.c.n.c.NacosPropertySourceBuilder      Ignore the empty nacos configuration and get it based on dataId[ais-ma] & group[DEFAULT_GROUP]
[ais-ks:************:8066] 2025-06-18 16:51:29.643 WARN 57064 trace_id=[][] [main] c.a.c.n.c.NacosPropertySourceBuilder      Ignore the empty nacos configuration and get it based on dataId[ais-ma.yaml] & group[DEFAULT_GROUP]
[ais-ks:************:8066] 2025-06-18 16:51:29.945 WARN 57064 trace_id=[][] [main] c.a.c.n.c.NacosPropertySourceBuilder      Ignore the empty nacos configuration and get it based on dataId[ais-ma-localyitiji.yaml] & group[DEFAULT_GROUP]
[ais-ks:************:8066] 2025-06-18 16:51:29.946 INFO 57064 trace_id=[][] [main] b.c.PropertySourceBootstrapConfiguration  Located property source: [BootstrapPropertySource {name='bootstrapProperties-ais-ma-localyitiji.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-ais-ma.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-ais-ma,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-telecom-ai-common.yaml,DEFAULT_GROUP'}]
[ais-ks:************:8066] 2025-06-18 16:51:29.962 INFO 57064 trace_id=[][] [main] com.ai.ais.ma.Starter                     The following 1 profile is active: "localyitiji"
[ais-ks:************:8066] 2025-06-18 16:51:31.411 INFO 57064 trace_id=[][] [main] .s.d.r.c.RepositoryConfigurationDelegate  Multiple Spring Data modules found, entering strict repository configuration mode
[ais-ks:************:8066] 2025-06-18 16:51:31.415 INFO 57064 trace_id=[][] [main] .s.d.r.c.RepositoryConfigurationDelegate  Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[ais-ks:************:8066] 2025-06-18 16:51:31.484 INFO 57064 trace_id=[][] [main] .s.d.r.c.RepositoryConfigurationDelegate  Finished Spring Data repository scanning in 48 ms. Found 0 Redis repository interfaces.
[ais-ks:************:8066] 2025-06-18 16:51:31.840 INFO 57064 trace_id=[][] [main] o.s.cloud.context.scope.GenericScope      BeanFactory id=5e9c01e1-0017-3b84-b949-47df645d3a02
[ais-ks:************:8066] 2025-06-18 16:51:31.911 INFO 57064 trace_id=[][] [main] ptablePropertiesBeanFactoryPostProcessor  Post-processing PropertySource instances
[ais-ks:************:8066] 2025-06-18 16:51:31.912 INFO 57064 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrapProperties-ais-ma-localyitiji.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:51:31.912 INFO 57064 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrapProperties-ais-ma.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:51:31.912 INFO 57064 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrapProperties-ais-ma,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:51:31.912 INFO 57064 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource bootstrapProperties-telecom-ai-common.yaml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:51:31.912 INFO 57064 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[ais-ks:************:8066] 2025-06-18 16:51:31.912 INFO 57064 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:51:31.912 INFO 57064 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:51:31.912 INFO 57064 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:51:31.912 INFO 57064 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:51:31.912 INFO 57064 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:51:31.912 INFO 57064 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:51:31.913 INFO 57064 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:51:31.913 INFO 57064 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [application-localyitiji.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:51:31.913 INFO 57064 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource Config resource 'class path resource [application.yaml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:51:31.913 INFO 57064 trace_id=[][] [main] c.u.j.EncryptablePropertySourceConverter  Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[ais-ks:************:8066] 2025-06-18 16:51:31.919 INFO 57064 trace_id=[][] [main] trationDelegate$BeanPostProcessorChecker  Bean 'hibernateValidatorConfiguration' of type [com.ai.ais.ma.config.validator.HibernateValidatorConfiguration$$EnhancerBySpringCGLIB$$552773da] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[ais-ks:************:8066] 2025-06-18 16:51:31.969 INFO 57064 trace_id=[][] [main] c.u.j.filter.DefaultLazyPropertyFilter    Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[ais-ks:************:8066] 2025-06-18 16:51:32.430 INFO 57064 trace_id=[][] [main] c.u.j.r.DefaultLazyPropertyResolver       Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[ais-ks:************:8066] 2025-06-18 16:51:32.430 INFO 57064 trace_id=[][] [main] c.u.j.d.DefaultLazyPropertyDetector       Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[ais-ks:************:8066] 2025-06-18 16:51:32.783 INFO 57064 trace_id=[][] [main] o.s.b.w.embedded.tomcat.TomcatWebServer   Tomcat initialized with port(s): 8066 (http)
[ais-ks:************:8066] 2025-06-18 16:51:32.800 INFO 57064 trace_id=[][] [main] o.a.coyote.http11.Http11NioProtocol       Initializing ProtocolHandler ["http-nio-8066"]
[ais-ks:************:8066] 2025-06-18 16:51:32.802 INFO 57064 trace_id=[][] [main] o.apache.catalina.core.StandardService    Starting service [Tomcat]
[ais-ks:************:8066] 2025-06-18 16:51:32.803 INFO 57064 trace_id=[][] [main] org.apache.catalina.core.StandardEngine   Starting Servlet engine: [Apache Tomcat/9.0.100]
[ais-ks:************:8066] 2025-06-18 16:51:32.829 WARN 57064 trace_id=[][] [main] o.a.c.webresources.DirResourceSet         Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8066.5748133749998269045] which is part of the web application [/ais/ma]
[ais-ks:************:8066] 2025-06-18 16:51:33.026 INFO 57064 trace_id=[][] [main] o.a.c.c.C.[.[localhost].[/ais/ma]         Initializing Spring embedded WebApplicationContext
[ais-ks:************:8066] 2025-06-18 16:51:33.027 INFO 57064 trace_id=[][] [main] w.s.c.ServletWebServerApplicationContext  Root WebApplicationContext: initialization completed in 3034 ms
[ais-ks:************:8066] 2025-06-18 16:51:33.132 INFO 57064 trace_id=[][] [main] c.a.a.m.c.c.c.RedissonConfiguration       init single server
[ais-ks:************:8066] 2025-06-18 16:51:33.514 INFO 57064 trace_id=[][] [main] org.redisson.Version                      Redisson 3.17.7
[ais-ks:************:8066] 2025-06-18 16:51:34.286 INFO 57064 trace_id=[][] [redisson-netty-2-5] o.r.c.pool.MasterPubSubConnectionPool     1 connections initialized for 127.0.0.1/127.0.0.1:6379
[ais-ks:************:8066] 2025-06-18 16:51:34.352 INFO 57064 trace_id=[][] [redisson-netty-2-19] o.r.c.pool.MasterConnectionPool           24 connections initialized for 127.0.0.1/127.0.0.1:6379
[ais-ks:************:8066] 2025-06-18 16:51:34.689 INFO 57064 trace_id=[][] [main] c.c.c.p.c.c.PlatformSsoConfiguration      start to init smartContainer
[ais-ks:************:8066] 2025-06-18 16:51:34.886 INFO 57064 trace_id=[][] [main] c.c.c.p.client.CloudPlatformContainer     start to init filter
[ais-ks:************:8066] 2025-06-18 16:51:35.347 INFO 57064 trace_id=[][] [main] o.s.c.openfeign.FeignClientFactoryBean    For 'telecom-ai-gs-engine-srv' URL not provided. Will try picking an instance via load-balancing.
[ais-ks:************:8066] 2025-06-18 16:51:37.079 INFO 57064 trace_id=[][] [main] com.zaxxer.hikari.HikariDataSource        HikariPool-1 - Starting...
[ais-ks:************:8066] 2025-06-18 16:51:37.274 INFO 57064 trace_id=[][] [main] com.zaxxer.hikari.HikariDataSource        HikariPool-1 - Start completed.
[ais-ks:************:8066] 2025-06-18 16:51:37.329 INFO 57064 trace_id=[][] [main] o.f.c.internal.license.VersionPrinter     Flyway Community Edition 8.2.0 by Redgate
[ais-ks:************:8066] 2025-06-18 16:51:37.330 INFO 57064 trace_id=[][] [main] o.f.c.i.database.base.BaseDatabaseType    Database: ***************************************** (MySQL 8.0)
[ais-ks:************:8066] 2025-06-18 16:51:37.465 INFO 57064 trace_id=[][] [main] o.f.core.internal.command.DbValidate      Successfully validated 1 migration (execution time 00:00.047s)
[ais-ks:************:8066] 2025-06-18 16:51:37.492 INFO 57064 trace_id=[][] [main] o.f.core.internal.command.DbMigrate       Current version of schema `ai_kms_ais_ma`: 1.001
[ais-ks:************:8066] 2025-06-18 16:51:37.494 INFO 57064 trace_id=[][] [main] o.f.core.internal.command.DbMigrate       Schema `ai_kms_ais_ma` is up to date. No migration necessary.
[ais-ks:************:8066] 2025-06-18 16:51:39.847 WARN 57064 trace_id=[][] [main] o.s.b.a.f.FreeMarkerAutoConfiguration     Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
[ais-ks:************:8066] 2025-06-18 16:51:41.306 INFO 57064 trace_id=[][] [main] o.a.coyote.http11.Http11NioProtocol       Starting ProtocolHandler ["http-nio-8066"]
[ais-ks:************:8066] 2025-06-18 16:51:41.319 INFO 57064 trace_id=[][] [main] o.s.b.w.embedded.tomcat.TomcatWebServer   Tomcat started on port(s): 8066 (http) with context path '/ais/ma'
[ais-ks:************:8066] 2025-06-18 16:51:41.905 INFO 57064 trace_id=[][] [main] s.a.ScheduledAnnotationBeanPostProcessor  No TaskScheduler/ScheduledExecutorService bean found for scheduled processing
[ais-ks:************:8066] 2025-06-18 16:51:41.923 INFO 57064 trace_id=[][] [main] com.ai.ais.ma.Starter                     Started Starter in 25.281 seconds (JVM running for 28.295)
[ais-ks:************:8066] 2025-06-18 16:51:41.928 INFO 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   即将注册API: 获取菜单信息, 分组 账号资源接口, GET /ais/ma/web/platform/menu 
[ais-ks:************:8066] 2025-06-18 16:51:42.185 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://***************:32088/platform/permission/register
[ais-ks:************:8066] 2025-06-18 16:51:42.185 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:************:8066] 2025-06-18 16:51:42.186 INFO 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   即将注册API: 删除AI文章报告, 分组 智能写作, POST /ais/ma/web/knowledge/ai-report/delete 
[ais-ks:************:8066] 2025-06-18 16:51:42.194 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://***************:32088/platform/permission/register
[ais-ks:************:8066] 2025-06-18 16:51:42.194 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:************:8066] 2025-06-18 16:51:42.194 INFO 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   即将注册API: 新增AI文章报告, 分组 智能写作, POST /ais/ma/web/knowledge/ai-report 
[ais-ks:************:8066] 2025-06-18 16:51:42.204 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://***************:32088/platform/permission/register
[ais-ks:************:8066] 2025-06-18 16:51:42.204 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:************:8066] 2025-06-18 16:51:42.204 INFO 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   即将注册API: 根据uuid获取swapSession信息, 分组 账号资源接口, GET /ais/ma/web/platform/swap/session/info 
[ais-ks:************:8066] 2025-06-18 16:51:42.213 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://***************:32088/platform/permission/register
[ais-ks:************:8066] 2025-06-18 16:51:42.213 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:************:8066] 2025-06-18 16:51:42.213 INFO 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   即将注册API: 获取授权跳转地址, 分组 账号资源接口, GET /ais/ma/web/platform/address 
[ais-ks:************:8066] 2025-06-18 16:51:42.222 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://***************:32088/platform/permission/register
[ais-ks:************:8066] 2025-06-18 16:51:42.222 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:************:8066] 2025-06-18 16:51:42.222 INFO 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   即将注册API: 退出登录, 分组 账号资源接口, GET /ais/ma/web/platform/logout 
[ais-ks:************:8066] 2025-06-18 16:51:42.234 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://***************:32088/platform/permission/register
[ais-ks:************:8066] 2025-06-18 16:51:42.235 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:************:8066] 2025-06-18 16:51:42.235 INFO 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   即将注册API: AI文章报告详情, 分组 智能写作, GET /ais/ma/web/knowledge/ai-report/* 
[ais-ks:************:8066] 2025-06-18 16:51:42.247 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://***************:32088/platform/permission/register
[ais-ks:************:8066] 2025-06-18 16:51:42.247 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:************:8066] 2025-06-18 16:51:42.247 INFO 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   即将注册API: 更新AI文章报告, 分组 智能写作, PUT /ais/ma/web/knowledge/ai-report/* 
[ais-ks:************:8066] 2025-06-18 16:51:42.257 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://***************:32088/platform/permission/register
[ais-ks:************:8066] 2025-06-18 16:51:42.257 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:************:8066] 2025-06-18 16:51:42.257 INFO 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   即将注册API: 大纲编写, 分组 智能写作, POST /ais/ma/web/knowledge/ai-report/outline 
[ais-ks:************:8066] 2025-06-18 16:51:42.266 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://***************:32088/platform/permission/register
[ais-ks:************:8066] 2025-06-18 16:51:42.266 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:************:8066] 2025-06-18 16:51:42.266 INFO 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   即将注册API: 系统版本信息查询, 分组 系统信息查询, GET /ais/ma/web/system/info 
[ais-ks:************:8066] 2025-06-18 16:51:42.276 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://***************:32088/platform/permission/register
[ais-ks:************:8066] 2025-06-18 16:51:42.276 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:************:8066] 2025-06-18 16:51:42.276 INFO 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   即将注册API: 获取按钮资源信息, 分组 账号资源接口, GET /ais/ma/web/platform/resource 
[ais-ks:************:8066] 2025-06-18 16:51:42.284 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://***************:32088/platform/permission/register
[ais-ks:************:8066] 2025-06-18 16:51:42.284 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:************:8066] 2025-06-18 16:51:42.284 INFO 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   即将注册API: 更新AI文章报告名称, 分组 智能写作, PUT /ais/ma/web/knowledge/ai-report/*/rename 
[ais-ks:************:8066] 2025-06-18 16:51:42.292 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://***************:32088/platform/permission/register
[ais-ks:************:8066] 2025-06-18 16:51:42.292 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:************:8066] 2025-06-18 16:51:42.292 INFO 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   即将注册API: 分页查询AI文章报告, 分组 智能写作, POST /ais/ma/web/knowledge/ai-report/page 
[ais-ks:************:8066] 2025-06-18 16:51:42.304 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://***************:32088/platform/permission/register
[ais-ks:************:8066] 2025-06-18 16:51:42.304 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:************:8066] 2025-06-18 16:51:42.304 INFO 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   即将注册API: AI报告正文编写, 分组 智能写作, POST /ais/ma/web/knowledge/ai-report/content 
[ais-ks:************:8066] 2025-06-18 16:51:42.313 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://***************:32088/platform/permission/register
[ais-ks:************:8066] 2025-06-18 16:51:42.314 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:************:8066] 2025-06-18 16:51:42.314 INFO 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   即将注册API: 获取用户信息, 分组 账号资源接口, GET /ais/ma/web/platform/user 
[ais-ks:************:8066] 2025-06-18 16:51:42.323 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://***************:32088/platform/permission/register
[ais-ks:************:8066] 2025-06-18 16:51:42.323 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:************:8066] 2025-06-18 16:51:42.323 INFO 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   即将注册API: 获取产品logo相关配置, 分组 账号资源接口, GET /ais/ma/web/platform/logo 
[ais-ks:************:8066] 2025-06-18 16:51:42.331 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://***************:32088/platform/permission/register
[ais-ks:************:8066] 2025-06-18 16:51:42.331 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:************:8066] 2025-06-18 16:51:42.355 INFO 57064 trace_id=[][] [main] c.a.c.n.refresh.NacosContextRefresher     [Nacos Config] Listening config: dataId=ais-ma-localyitiji.yaml, group=DEFAULT_GROUP
[ais-ks:************:8066] 2025-06-18 16:51:42.355 INFO 57064 trace_id=[][] [main] c.a.c.n.refresh.NacosContextRefresher     [Nacos Config] Listening config: dataId=ais-ma, group=DEFAULT_GROUP
[ais-ks:************:8066] 2025-06-18 16:51:42.356 INFO 57064 trace_id=[][] [main] c.a.c.n.refresh.NacosContextRefresher     [Nacos Config] Listening config: dataId=ais-ma.yaml, group=DEFAULT_GROUP
[ais-ks:************:8066] 2025-06-18 16:53:03.238 INFO 57064 trace_id=[][] [http-nio-8066-exec-2] o.a.c.c.C.[.[localhost].[/ais/ma]         Initializing Spring DispatcherServlet 'dispatcherServlet'
[ais-ks:************:8066] 2025-06-18 16:53:03.238 INFO 57064 trace_id=[][] [http-nio-8066-exec-2] o.s.web.servlet.DispatcherServlet         Initializing Servlet 'dispatcherServlet'
[ais-ks:************:8066] 2025-06-18 16:53:03.241 INFO 57064 trace_id=[][] [http-nio-8066-exec-2] o.s.web.servlet.DispatcherServlet         Completed initialization in 3 ms
[ais-ks:************:8066] 2025-06-18 16:53:03.256 INFO 57064 trace_id=[][] [http-nio-8066-exec-2] c.c.c.p.client.filter.LoginFilter         request url is [url=http://localhost:8066/ais/ma/web/knowledge/ai-report/outline]
[ais-ks:************:8066] 2025-06-18 16:53:03.267 INFO 57064 trace_id=[][] [http-nio-8066-exec-2] c.c.c.p.client.filter.LoginFilter         校验信息：url=http://localhost:8066/ais/ma/web/knowledge/ai-report/outline, loginType=unknown, session=e1ff308f-32d8-4946-b084-666adbe3b310
[ais-ks:************:8066] 2025-06-18 16:53:03.269 INFO 57064 trace_id=[][] [http-nio-8066-exec-2] c.c.c.p.client.util.AccessTokenUtils      从Cookie中获取到sessionId: 6120e698-bc0d-4e53-a22e-fb8def3f79c8
[ais-ks:************:8066] 2025-06-18 16:53:03.289 INFO 57064 trace_id=[][] [http-nio-8066-exec-2] c.c.c.p.c.s.r.RedisSessionMappingStorage  session=6120e698-bc0d-4e53-a22e-fb8def3f79c8, value=null
[ais-ks:************:8066] 2025-06-18 16:53:03.324 INFO 57064 trace_id=[][] [http-nio-8066-exec-2] c.c.c.platform.client.util.HttpsUtils     [http://***************:32088/platform/oauth2/token/bySession]: Response entity is [{"code":1,"msg":"ok","data":{"accessToken":"P-AT-fa0d187ef16448bb91d7913ecea1c0e1","expiresIn":43200,"refreshToken":"P-RT-9b59fe9f7fc84e1282fa11a104e41e22","user":{"corpCode":"yitiji","userId":"8621317740912771072","thirdUserId":"8621317740912771072","username":"admin","name":"admin","isManager":1,"defaultTeamCode":null,"rootTeamCode":"yitiji","isAdmin":true,"ip":null,"teamCode":null,"teamName":null,"teamRolesList":[],"teamId":null,"isSuperAdmin":false},"expirationTime":1750257009699,"expired":false}}]
[ais-ks:************:8066] 2025-06-18 16:53:03.377 INFO 57064 trace_id=[][] [http-nio-8066-exec-2] c.c.c.p.client.util.AccessTokenUtils      从统一中心获取sessionId[6120e698-bc0d-4e53-a22e-fb8def3f79c8]对应的accessToken[{"accessToken":"P-AT-fa0d187ef16448bb91d7913ecea1c0e1","expirationTime":1750257009699,"expired":false,"expiresIn":43200,"refreshToken":"P-RT-9b59fe9f7fc84e1282fa11a104e41e22","user":{"corpCode":"yitiji","isAdmin":true,"isManager":1,"isSuperAdmin":false,"name":"admin","rootTeamCode":"yitiji","teamRolesList":[],"thirdUserId":"8621317740912771072","userId":"8621317740912771072","username":"admin"}}]
[ais-ks:************:8066] 2025-06-18 16:53:03.600 INFO 57064 trace_id=[][] [http-nio-8066-exec-2] c.a.a.m.c.cache.aspect.DebugLogAspect     AI报告大纲编写|request|[{"themeText":"公积金办理","source":"INIT","outline":"","language":null}]
[ais-ks:************:8066] 2025-06-18 16:53:03.631 INFO 57064 trace_id=[][] [http-nio-8066-exec-2] c.a.a.m.c.cache.aspect.DebugLogAspect     AI报告大纲编写|response|{"timeout":300000}
[ais-ks:************:8066] 2025-06-18 16:53:29.129 INFO 57064 trace_id=[][] [OkHttp http://***************:31170/...] c.a.a.m.c.s.watcher.AbstractSseWatcher    ========= sse连接开启 =========
[ais-ks:************:8066] 2025-06-18 16:54:06.415 INFO 57064 trace_id=[][] [OkHttp http://***************:31170/...] c.a.a.m.c.s.watcher.AbstractSseWatcher    ========= sse连接关闭 =========
[ais-ks:************:8066] 2025-06-18 16:56:06.104 INFO 57064 trace_id=[][] [http-nio-8066-exec-4] c.c.c.p.client.filter.LoginFilter         request url is [url=http://localhost:8066/ais/ma/web/knowledge/ai-report/outline]
[ais-ks:************:8066] 2025-06-18 16:56:06.112 INFO 57064 trace_id=[][] [http-nio-8066-exec-4] c.c.c.p.client.filter.LoginFilter         校验信息：url=http://localhost:8066/ais/ma/web/knowledge/ai-report/outline, loginType=unknown, session=e1ff308f-32d8-4946-b084-666adbe3b310
[ais-ks:************:8066] 2025-06-18 16:56:06.112 INFO 57064 trace_id=[][] [http-nio-8066-exec-4] c.c.c.p.client.util.AccessTokenUtils      从Cookie中获取到sessionId: 6120e698-bc0d-4e53-a22e-fb8def3f79c8
[ais-ks:************:8066] 2025-06-18 16:56:06.114 INFO 57064 trace_id=[][] [http-nio-8066-exec-4] c.c.c.p.c.s.r.RedisSessionMappingStorage  session=6120e698-bc0d-4e53-a22e-fb8def3f79c8, value=null
[ais-ks:************:8066] 2025-06-18 16:56:06.132 INFO 57064 trace_id=[][] [http-nio-8066-exec-4] c.c.c.platform.client.util.HttpsUtils     [http://***************:32088/platform/oauth2/token/bySession]: Response entity is [{"code":1,"msg":"ok","data":{"accessToken":"P-AT-fa0d187ef16448bb91d7913ecea1c0e1","expiresIn":43200,"refreshToken":"P-RT-9b59fe9f7fc84e1282fa11a104e41e22","user":{"corpCode":"yitiji","userId":"8621317740912771072","thirdUserId":"8621317740912771072","username":"admin","name":"admin","isManager":1,"defaultTeamCode":null,"rootTeamCode":"yitiji","isAdmin":true,"ip":null,"teamCode":null,"teamName":null,"teamRolesList":[],"teamId":null,"isSuperAdmin":false},"expirationTime":1750257009699,"expired":false}}]
[ais-ks:************:8066] 2025-06-18 16:56:06.134 INFO 57064 trace_id=[][] [http-nio-8066-exec-4] c.c.c.p.client.util.AccessTokenUtils      从统一中心获取sessionId[6120e698-bc0d-4e53-a22e-fb8def3f79c8]对应的accessToken[{"accessToken":"P-AT-fa0d187ef16448bb91d7913ecea1c0e1","expirationTime":1750257009699,"expired":false,"expiresIn":43200,"refreshToken":"P-RT-9b59fe9f7fc84e1282fa11a104e41e22","user":{"corpCode":"yitiji","isAdmin":true,"isManager":1,"isSuperAdmin":false,"name":"admin","rootTeamCode":"yitiji","teamRolesList":[],"thirdUserId":"8621317740912771072","userId":"8621317740912771072","username":"admin"}}]
[ais-ks:************:8066] 2025-06-18 16:56:06.139 INFO 57064 trace_id=[][] [http-nio-8066-exec-4] c.a.a.m.c.cache.aspect.DebugLogAspect     AI报告大纲编写|request|[{"themeText":"公积金办理","source":"INIT","outline":"","language":null}]
[ais-ks:************:8066] 2025-06-18 16:56:06.143 INFO 57064 trace_id=[][] [http-nio-8066-exec-4] c.a.a.m.c.cache.aspect.DebugLogAspect     AI报告大纲编写|response|{"timeout":300000}
[ais-ks:************:8066] 2025-06-18 16:56:32.886 INFO 57064 trace_id=[][] [OkHttp http://***************:31170/...] c.a.a.m.c.s.watcher.AbstractSseWatcher    ========= sse连接开启 =========
[ais-ks:************:8066] 2025-06-18 16:56:32.960 WARN 57064 trace_id=[][] [OkHttp http://***************:31170/...] c.a.a.m.c.s.watcher.AbstractSseWatcher    用户主动关闭管道
[ais-ks:************:8066] 2025-06-18 16:56:32.960 WARN 57064 trace_id=[][] [http-nio-8066-exec-5] c.a.ais.ma.common.sse.SseEmitterBuilder   sse消息发送异常，可能是用户手动中断或网络原因，该错误将忽略
[ais-ks:************:8066] 2025-06-18 16:56:32.972 ERROR 57064 trace_id=[][] [http-nio-8066-exec-5] c.a.a.m.c.mvc.GlobalExceptionHandler      IO异常

java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method) ~[na:1.8.0_452]
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51) ~[na:1.8.0_452]
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93) ~[na:1.8.0_452]
	at sun.nio.ch.IOUtil.write(IOUtil.java:65) ~[na:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:470) ~[na:1.8.0_452]
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:140) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1430) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:775) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:739) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:723) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:574) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:156) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:216) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1273) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:407) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.Response.action(Response.java:207) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:304) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:270) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:133) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.springframework.session.web.http.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:499) ~[spring-session-core-2.7.4.jar:2.7.4]
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.flush(StandardServletAsyncWebRequest.java:389) ~[spring-web-5.3.37.jar:5.3.37]
	at sun.nio.cs.StreamEncoder.implFlush(StreamEncoder.java:297) ~[na:1.8.0_452]
	at sun.nio.cs.StreamEncoder.flush(StreamEncoder.java:141) ~[na:1.8.0_452]
	at java.io.OutputStreamWriter.flush(OutputStreamWriter.java:229) ~[na:1.8.0_452]
	at org.springframework.util.StreamUtils.copy(StreamUtils.java:148) ~[spring-core-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.StringHttpMessageConverter.writeInternal(StringHttpMessageConverter.java:125) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.StringHttpMessageConverter.writeInternal(StringHttpMessageConverter.java:44) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.AbstractHttpMessageConverter.write(AbstractHttpMessageConverter.java:227) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler$HttpMessageConvertingHandler.sendInternal(ResponseBodyEmitterReturnValueHandler.java:212) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler$HttpMessageConvertingHandler.send(ResponseBodyEmitterReturnValueHandler.java:205) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter.sendInternal(ResponseBodyEmitter.java:204) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter.send(ResponseBodyEmitter.java:198) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.SseEmitter.send(SseEmitter.java:127) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at com.ai.ais.ma.utils.sse.GsOutlineStreamWatcher.onEvent(GsOutlineStreamWatcher.java:78) ~[classes/:na]
	at okhttp3.internal.sse.RealEventSource.onEvent(RealEventSource.kt:101) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.ServerSentEventReader.completeEvent(ServerSentEventReader.kt:108) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.ServerSentEventReader.processNextEvent(ServerSentEventReader.kt:52) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.RealEventSource.processResponse(RealEventSource.kt:75) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.RealEventSource.onResponse(RealEventSource.kt:46) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519) ~[okhttp-4.9.3.jar:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:60) ~[transmittable-thread-local-2.14.1.jar:na]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_452]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_452]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_452]

[ais-ks:************:8066] 2025-06-18 16:56:32.974 WARN 57064 trace_id=[][] [http-nio-8066-exec-5] .m.m.a.ExceptionHandlerExceptionResolver  Failure in @ExceptionHandler com.ai.ais.ma.config.mvc.GlobalExceptionHandler#handlerIOException(IOException, HttpServletResponse)

org.springframework.web.context.request.async.AsyncRequestNotUsableException: Response not usable after response errors.
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleHttpServletResponse.obtainLockAndCheckState(StandardServletAsyncWebRequest.java:313) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleHttpServletResponse.getWriter(StandardServletAsyncWebRequest.java:266) ~[spring-web-5.3.37.jar:5.3.37]
	at javax.servlet.ServletResponseWrapper.getWriter(ServletResponseWrapper.java:117) ~[javax.servlet-api-4.0.1.jar:4.0.1]
	at org.springframework.session.web.http.OnCommittedResponseWrapper.getWriter(OnCommittedResponseWrapper.java:152) ~[spring-session-core-2.7.4.jar:2.7.4]
	at com.ai.ais.ma.common.utils.InterceptorUtils.writeError(InterceptorUtils.java:37) ~[classes/:na]
	at com.ai.ais.ma.config.mvc.GlobalExceptionHandler.handlerIOException(GlobalExceptionHandler.java:121) ~[classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_452]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:428) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:75) [spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:142) [spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80) [spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1332) [spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1143) [spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089) [spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:665) [javax.servlet-api-4.0.1.jar:4.0.1]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750) [javax.servlet-api-4.0.1.jar:4.0.1]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102) [spring-web-5.3.37.jar:5.3.37]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.37.jar:5.3.37]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.37.jar:5.3.37]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102) [spring-web-5.3.37.jar:5.3.37]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:142) [spring-session-core-2.7.4.jar:2.7.4]
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:82) [spring-session-core-2.7.4.jar:2.7.4]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) [spring-web-5.3.37.jar:5.3.37]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) [spring-web-5.3.37.jar:5.3.37]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:102) [spring-web-5.3.37.jar:5.3.37]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:642) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.core.ApplicationDispatcher.doDispatch(ApplicationDispatcher.java:570) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.core.ApplicationDispatcher.dispatch(ApplicationDispatcher.java:539) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.core.AsyncContextImpl$AsyncRunnable.run(AsyncContextImpl.java:600) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.core.AsyncContextImpl.doInternalDispatch(AsyncContextImpl.java:343) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:166) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.CoyoteAdapter.asyncDispatch(CoyoteAdapter.java:241) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.AbstractProcessor.dispatch(AbstractProcessor.java:242) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:937) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1793) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) [tomcat-embed-core-9.0.100.jar:9.0.100]
	at java.lang.Thread.run(Thread.java:750) [na:1.8.0_452]

[ais-ks:************:8066] 2025-06-18 16:56:32.985 ERROR 57064 trace_id=[][] [http-nio-8066-exec-5] o.a.c.c.C.[.[.[.[dispatcherServlet]       Servlet.service() for servlet [dispatcherServlet] threw exception

java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method) ~[na:1.8.0_452]
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51) ~[na:1.8.0_452]
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93) ~[na:1.8.0_452]
	at sun.nio.ch.IOUtil.write(IOUtil.java:65) ~[na:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:470) ~[na:1.8.0_452]
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:140) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1430) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:775) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:739) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:723) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:574) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:156) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:216) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1273) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:407) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.Response.action(Response.java:207) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:304) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:270) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:133) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.springframework.session.web.http.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:499) ~[spring-session-core-2.7.4.jar:2.7.4]
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.flush(StandardServletAsyncWebRequest.java:389) ~[spring-web-5.3.37.jar:5.3.37]
	at sun.nio.cs.StreamEncoder.implFlush(StreamEncoder.java:297) ~[na:1.8.0_452]
	at sun.nio.cs.StreamEncoder.flush(StreamEncoder.java:141) ~[na:1.8.0_452]
	at java.io.OutputStreamWriter.flush(OutputStreamWriter.java:229) ~[na:1.8.0_452]
	at org.springframework.util.StreamUtils.copy(StreamUtils.java:148) ~[spring-core-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.StringHttpMessageConverter.writeInternal(StringHttpMessageConverter.java:125) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.StringHttpMessageConverter.writeInternal(StringHttpMessageConverter.java:44) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.AbstractHttpMessageConverter.write(AbstractHttpMessageConverter.java:227) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler$HttpMessageConvertingHandler.sendInternal(ResponseBodyEmitterReturnValueHandler.java:212) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler$HttpMessageConvertingHandler.send(ResponseBodyEmitterReturnValueHandler.java:205) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter.sendInternal(ResponseBodyEmitter.java:204) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter.send(ResponseBodyEmitter.java:198) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.SseEmitter.send(SseEmitter.java:127) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at com.ai.ais.ma.utils.sse.GsOutlineStreamWatcher.onEvent(GsOutlineStreamWatcher.java:78) ~[classes/:na]
	at okhttp3.internal.sse.RealEventSource.onEvent(RealEventSource.kt:101) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.ServerSentEventReader.completeEvent(ServerSentEventReader.kt:108) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.ServerSentEventReader.processNextEvent(ServerSentEventReader.kt:52) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.RealEventSource.processResponse(RealEventSource.kt:75) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.RealEventSource.onResponse(RealEventSource.kt:46) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519) ~[okhttp-4.9.3.jar:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:60) ~[transmittable-thread-local-2.14.1.jar:na]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_452]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_452]
	at java.lang.Thread.run(Thread.java:750) [na:1.8.0_452]

[ais-ks:************:8066] 2025-06-18 16:56:32.985 ERROR 57064 trace_id=[][] [http-nio-8066-exec-5] o.a.c.c.C.[.[.[.[dispatcherServlet]       Servlet.service() for servlet [dispatcherServlet] in context with path [/ais/ma] threw exception

java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method) ~[na:1.8.0_452]
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51) ~[na:1.8.0_452]
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93) ~[na:1.8.0_452]
	at sun.nio.ch.IOUtil.write(IOUtil.java:65) ~[na:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:470) ~[na:1.8.0_452]
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:140) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1430) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:775) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:739) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:723) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:574) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:156) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:216) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1273) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:407) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.Response.action(Response.java:207) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:304) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:270) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:133) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.springframework.session.web.http.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:499) ~[spring-session-core-2.7.4.jar:2.7.4]
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.flush(StandardServletAsyncWebRequest.java:389) ~[spring-web-5.3.37.jar:5.3.37]
	at sun.nio.cs.StreamEncoder.implFlush(StreamEncoder.java:297) ~[na:1.8.0_452]
	at sun.nio.cs.StreamEncoder.flush(StreamEncoder.java:141) ~[na:1.8.0_452]
	at java.io.OutputStreamWriter.flush(OutputStreamWriter.java:229) ~[na:1.8.0_452]
	at org.springframework.util.StreamUtils.copy(StreamUtils.java:148) ~[spring-core-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.StringHttpMessageConverter.writeInternal(StringHttpMessageConverter.java:125) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.StringHttpMessageConverter.writeInternal(StringHttpMessageConverter.java:44) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.AbstractHttpMessageConverter.write(AbstractHttpMessageConverter.java:227) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler$HttpMessageConvertingHandler.sendInternal(ResponseBodyEmitterReturnValueHandler.java:212) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler$HttpMessageConvertingHandler.send(ResponseBodyEmitterReturnValueHandler.java:205) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter.sendInternal(ResponseBodyEmitter.java:204) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter.send(ResponseBodyEmitter.java:198) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.SseEmitter.send(SseEmitter.java:127) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at com.ai.ais.ma.utils.sse.GsOutlineStreamWatcher.onEvent(GsOutlineStreamWatcher.java:78) ~[classes/:na]
	at okhttp3.internal.sse.RealEventSource.onEvent(RealEventSource.kt:101) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.ServerSentEventReader.completeEvent(ServerSentEventReader.kt:108) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.ServerSentEventReader.processNextEvent(ServerSentEventReader.kt:52) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.RealEventSource.processResponse(RealEventSource.kt:75) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.RealEventSource.onResponse(RealEventSource.kt:46) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519) ~[okhttp-4.9.3.jar:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:60) ~[transmittable-thread-local-2.14.1.jar:na]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_452]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_452]
	at java.lang.Thread.run(Thread.java:750) [na:1.8.0_452]

[ais-ks:************:8066] 2025-06-18 16:56:51.723 INFO 57064 trace_id=[][] [http-nio-8066-exec-6] c.c.c.p.client.filter.LoginFilter         request url is [url=http://localhost:8066/ais/ma/web/knowledge/ai-report/outline]
[ais-ks:************:8066] 2025-06-18 16:56:51.727 INFO 57064 trace_id=[][] [http-nio-8066-exec-6] c.c.c.p.client.filter.LoginFilter         校验信息：url=http://localhost:8066/ais/ma/web/knowledge/ai-report/outline, loginType=unknown, session=e1ff308f-32d8-4946-b084-666adbe3b310
[ais-ks:************:8066] 2025-06-18 16:56:51.727 INFO 57064 trace_id=[][] [http-nio-8066-exec-6] c.c.c.p.client.util.AccessTokenUtils      从Cookie中获取到sessionId: 6120e698-bc0d-4e53-a22e-fb8def3f79c8
[ais-ks:************:8066] 2025-06-18 16:56:51.728 INFO 57064 trace_id=[][] [http-nio-8066-exec-6] c.c.c.p.c.s.r.RedisSessionMappingStorage  session=6120e698-bc0d-4e53-a22e-fb8def3f79c8, value=null
[ais-ks:************:8066] 2025-06-18 16:56:51.742 INFO 57064 trace_id=[][] [http-nio-8066-exec-6] c.c.c.platform.client.util.HttpsUtils     [http://***************:32088/platform/oauth2/token/bySession]: Response entity is [{"code":1,"msg":"ok","data":{"accessToken":"P-AT-fa0d187ef16448bb91d7913ecea1c0e1","expiresIn":43200,"refreshToken":"P-RT-9b59fe9f7fc84e1282fa11a104e41e22","user":{"corpCode":"yitiji","userId":"8621317740912771072","thirdUserId":"8621317740912771072","username":"admin","name":"admin","isManager":1,"defaultTeamCode":null,"rootTeamCode":"yitiji","isAdmin":true,"ip":null,"teamCode":null,"teamName":null,"teamRolesList":[],"teamId":null,"isSuperAdmin":false},"expirationTime":1750257009699,"expired":false}}]
[ais-ks:************:8066] 2025-06-18 16:56:51.742 INFO 57064 trace_id=[][] [http-nio-8066-exec-6] c.c.c.p.client.util.AccessTokenUtils      从统一中心获取sessionId[6120e698-bc0d-4e53-a22e-fb8def3f79c8]对应的accessToken[{"accessToken":"P-AT-fa0d187ef16448bb91d7913ecea1c0e1","expirationTime":1750257009699,"expired":false,"expiresIn":43200,"refreshToken":"P-RT-9b59fe9f7fc84e1282fa11a104e41e22","user":{"corpCode":"yitiji","isAdmin":true,"isManager":1,"isSuperAdmin":false,"name":"admin","rootTeamCode":"yitiji","teamRolesList":[],"thirdUserId":"8621317740912771072","userId":"8621317740912771072","username":"admin"}}]
[ais-ks:************:8066] 2025-06-18 16:56:51.745 INFO 57064 trace_id=[][] [http-nio-8066-exec-6] c.a.a.m.c.cache.aspect.DebugLogAspect     AI报告大纲编写|request|[{"themeText":"公积金办理","source":"INIT","outline":"","language":null}]
[ais-ks:************:8066] 2025-06-18 16:56:51.746 INFO 57064 trace_id=[][] [http-nio-8066-exec-6] c.a.a.m.c.cache.aspect.DebugLogAspect     AI报告大纲编写|response|{"timeout":300000}
[ais-ks:************:8066] 2025-06-18 16:57:26.514 INFO 57064 trace_id=[][] [OkHttp http://***************:31170/...] c.a.a.m.c.s.watcher.AbstractSseWatcher    ========= sse连接开启 =========
[ais-ks:************:8066] 2025-06-18 16:58:04.556 INFO 57064 trace_id=[][] [OkHttp http://***************:31170/...] c.a.a.m.c.s.watcher.AbstractSseWatcher    ========= sse连接关闭 =========
