package com.ai.ais.ma.config.mvc;

import com.ai.ais.ma.common.utils.JsonUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * JacksonConfig
 *
 * <AUTHOR>
 * @date 2023-04-10 16:07
 */
@Configuration
public class JacksonConfig {

    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        return JsonUtils.newInstance();
    }

}
