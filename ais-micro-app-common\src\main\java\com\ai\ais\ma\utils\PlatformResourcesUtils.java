package com.ai.ais.ma.utils;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.chinatelecom.cloud.common.pojo.BaseResult;
import com.chinatelecom.cloud.platform.client.constant.Oauth2Constant;
import com.chinatelecom.cloud.platform.client.constant.SsoConstant;
import com.chinatelecom.cloud.platform.client.rpc.Menu;
import com.chinatelecom.cloud.platform.client.util.HttpUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

import static com.chinatelecom.cloud.platform.client.util.PlatformParamsUtil.buildBaseMap;
import static com.chinatelecom.cloud.platform.client.util.PlatformParamsUtil.ssoProperties;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年04月10日
 */
@Slf4j
public class PlatformResourcesUtils {


    /**
     * 登录成功后，获取用户所拥有的菜单列表
     *
     * @return
     */
    public static BaseResult<List<Menu>> getMenuList(
            String corpCode, String userId) {
        Map<String, String> paramMap = buildBaseMap();
        paramMap.put(Oauth2Constant.CORP_CODE, corpCode);
        paramMap.put(SsoConstant.USER_ID, userId);
        BaseResult<List<Menu>> menuHttp = getHttp(ssoProperties()
                        .getServerUrl(true) + SsoConstant.MENU_AUTH_URL, paramMap,
                new TypeReference<BaseResult<List<Menu>>>() {
                });
        if (menuHttp != null && menuHttp.ifSuccess()) {
            return BaseResult.of(menuHttp.getData());
        } else {
            return menuHttp;
        }
    }

    private static <T> T getHttp(String url, Map<String, String> paramMap, TypeReference<T> typeReference) {
        String jsonStr = HttpUtils.get(url, paramMap);
        if (jsonStr == null || jsonStr.isEmpty()) {
            log.error("getHttpAuth exception, return null. url:{}", url);
            return null;
        }
        return JSONObject.parseObject(jsonStr, typeReference);
    }


}
