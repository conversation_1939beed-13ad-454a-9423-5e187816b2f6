package com.ai.ais.ma.base.infra;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;

/**
 * BaseEntity
 *
 * <AUTHOR>
 * @date 2023-04-03 19:06
 */
@Getter
@Setter
public class BaseCodeEntity extends RootEntity {

    /**
     * 数据唯一编码
     */
    @TableField(value = "`code`", fill = FieldFill.INSERT)
    protected String code;


}
