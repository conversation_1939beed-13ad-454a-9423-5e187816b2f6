package com.ai.ais.ma.repository;


import com.ai.ais.ma.base.infra.repository.BaseByCodeRepository;
import com.ai.ais.ma.infra.po.KsKnowledgeReportPO;
import com.ai.ais.ma.model.dto.KsKnowledgeReportDTO;
import com.ai.ais.ma.model.vo.KsKnowledgeRenameParam;
import com.ai.ais.ma.model.vo.KsKnowledgeReportUpdateParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.base.BaseNameVO;

import java.util.List;

/**
 * <p>
 * 知识报告表 Repository接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */


public interface KsKnowledgeReportRepository extends BaseByCodeRepository<KsKnowledgeReportDTO, KsKnowledgeReportPO> {

    void updateName(String code, KsKnowledgeRenameParam updateParam);

    List<BaseNameVO> listNameAndCodeByCodes(List<String> codes);

    boolean updateOneByCode(String code, KsKnowledgeReportUpdateParam updateParam);

}

