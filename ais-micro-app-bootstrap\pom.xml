<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.ai</groupId>
        <artifactId>ais-micro-app</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.ai</groupId>
    <artifactId>ais-micro-app-bootstrap</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>com.ai</groupId>
            <artifactId>ais-micro-app-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ai</groupId>
            <artifactId>ais-micro-app-ppt</artifactId>
        </dependency>


    </dependencies>


    <build>
        <finalName>ais-micro-app</finalName>
        <plugins>
            <plugin>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-maven-plugin</artifactId>
                <version>3.0.3</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.ai.ais.ma.Starter</mainClass>
                    <layout>ZIP</layout>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>cn.telecom</groupId>
                <artifactId>classfinal-maven-plugin</artifactId>
                <version>1.2.1</version>
                <configuration>
                    <!-- 加密密码，加密打包之后pom.xml会被删除，不用担心在jar包里找到此密码-->
                    <password>jUDjme93kDU_d*df3</password>
                    <!-- 需要加密的包。多个以逗号,分割 -->
                    <packages>com.ai.ais</packages>
                    <!-- 需要加密的配置文件。多个以逗号,分割 -->
                    <cfgfiles>application.yml,bootstrap.yml</cfgfiles>
                    <!-- 加密依赖的第三方jar包。多个以逗号,分割 -->
                    <libjars>ais-micro-app-*.jar</libjars>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>classFinal</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


</project>
