package com.ai.ais.ma.common.sse;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年02月14日
 */
@Slf4j
public class SseEmitterBuilder extends SseEmitter {

    /**
     * 构造SseEmitter
     *
     * @return
     */
    public static SseEmitter buildSseEmitter() {
        SseEmitter sseEmitter = new SseEmitter(5 * 60 * 1000L);
        sseEmitter.onError(exception -> {
            if (exception instanceof IOException) {
                log.warn("sse消息发送异常，可能是用户手动中断或网络原因，该错误将忽略");
            } else {
                log.error("sse消息发送异常", exception);
            }
        });
        sseEmitter.onTimeout(() -> {
            log.error("sse消息发送超时");
        });
        return sseEmitter;
    }

}
