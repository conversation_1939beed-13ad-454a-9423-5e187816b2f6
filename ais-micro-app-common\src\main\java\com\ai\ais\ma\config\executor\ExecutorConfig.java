package com.ai.ais.ma.config.executor;

import com.ai.ais.ma.common.utils.ThreadUtils;
import com.ai.ais.ma.config.property.KsGlobalConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月06日
 */
@Configuration
@Slf4j
public class ExecutorConfig {

    @Resource
    private KsGlobalConfig ksGlobalConfig;

    /**
     * 默认线程池
     *
     * @return
     */
    @Bean("defaultPoolExecutor")
    public ExecutorService defaultPoolExecutor() {
        return ThreadUtils.newPool(ksGlobalConfig.getPool().getDefaultPool());
    }

    /**
     * sse默认线程池
     *
     * @return
     */
    @Bean("ssePoolExecutor")
    public ExecutorService ssePoolExecutor() {
        return ThreadUtils.newPool(ksGlobalConfig.getPool().getSsePool());
    }


}
