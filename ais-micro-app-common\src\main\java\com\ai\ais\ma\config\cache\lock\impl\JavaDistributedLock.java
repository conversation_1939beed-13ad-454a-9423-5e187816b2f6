package com.ai.ais.ma.config.cache.lock.impl;

import com.ai.ais.ma.config.cache.lock.DistributedLock;
import com.ai.ais.ma.config.cache.lock.ZLock;
import com.ai.ais.ma.config.property.KsGlobalConfig;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

/**
 * CaffeineDistributedLock
 * 单实例部署时使用的锁，单实例放弃redis的依赖
 *
 * <AUTHOR>
 * @date 2022-11-13 21:46
 */
public class JavaDistributedLock implements DistributedLock {

    private static final int ALLOWED_THREADS = 1;

    private ConcurrentHashMap<String, ZLock> lockPool = new ConcurrentHashMap<>();

    @Resource
    private KsGlobalConfig ksGlobalConfig;

    private ZLock getLock(String key, boolean isFair) {
        String lockKey = StringUtils.join(ksGlobalConfig.getSystem().getEnv(), ":lock:", ":", key);
        return new ZLock(lockKey, new Semaphore(ALLOWED_THREADS, isFair), this);
    }

    /**
     * @param key       锁的key
     * @param leaseTime 当前JVM级别的实现暂不支持，主要是为了临界区的安全考虑，分布式情况下可能有连接
     *                  中断形成死锁的场景，这种才进行支持
     * @param unit      {@code leaseTime} 参数的时间单位
     * @param isFair    是否公平锁
     * @return
     * @throws Exception
     */
    @Override
    public ZLock lock(String key, long leaseTime, TimeUnit unit, boolean isFair) throws Exception {
        ZLock zLock = lockPool.compute(key, (k, v) -> v == null ? getLock(key, isFair) : v);
        ((Semaphore) zLock.getLock()).acquireUninterruptibly();
        return zLock;
    }

    /**
     * @param key       锁的key
     * @param waitTime  获取锁的最大尝试时间(单位 {@code unit})
     * @param leaseTime 当前JVM级别的实现暂不支持，主要是为了临界区的安全考虑，分布式情况下可能有连接
     *                  中断形成死锁的场景，这种才进行支持
     * @param unit      {@code waitTime} 和 {@code leaseTime} 参数的时间单位
     * @param isFair
     * @return
     * @throws Exception
     */
    @Override
    public ZLock tryLock(String key, long waitTime, long leaseTime, TimeUnit unit, boolean isFair) throws Exception {
        ZLock zLock = lockPool.compute(key, (k, v) -> v == null ? getLock(key, isFair) : v);
        boolean result = ((Semaphore) zLock.getLock()).tryAcquire(waitTime, unit);
        return result ? zLock : null;
    }

    @Override
    public void unlock(ZLock zlock) {
        if (zlock != null) {
            Semaphore semaphore = (Semaphore) zlock.getLock();
            if (!semaphore.hasQueuedThreads()) {
                lockPool.remove(zlock.getKey(), zlock);
            }
            semaphore.release();
        }
    }
}
