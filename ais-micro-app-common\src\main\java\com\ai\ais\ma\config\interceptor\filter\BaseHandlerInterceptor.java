package com.ai.ais.ma.config.interceptor.filter;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月31日
 */
public interface BaseHandlerInterceptor {


    /**
     * 调用前拦截
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    default boolean preHandle(ServletRequest request, ServletResponse response) throws Exception {
        return true;
    }

    /**
     * 调用后拦截
     *
     * @param request
     * @param response
     * @throws Exception
     */
    default void postHandle(ServletRequest request, ServletResponse response) throws Exception {
    }

    /**
     * 完成后调用
     *
     * @param request
     * @param response
     * @param ex
     * @throws Exception
     */
    default void afterCompletion(ServletRequest request, ServletResponse response, Exception ex) throws Exception {
    }

}
