package com.ai.ais.ma.rpc.service.impl;

import com.ai.ais.ma.common.sse.SseEmitterUTF8;
import com.ai.ais.ma.common.sse.watcher.gs.GsSseProxyWatcher;
import com.ai.ais.ma.common.sse.watcher.gs.GsSseSyncWatcher;
import com.ai.ais.ma.common.utils.BizAssert;
import com.ai.ais.ma.common.utils.JsonUtils;
import com.ai.ais.ma.config.property.KsGlobalConfig;
import com.ai.ais.ma.model.vo.KsReportMessage;
import com.ai.ais.ma.rpc.service.GsPromptRpcService;
import com.alibaba.ttl.TtlRunnable;
import com.chinatelecom.gs.engine.kms.sdk.enums.ResponseType;
import com.chinatelecom.gs.engine.kms.sdk.enums.SseEventType;
import com.chinatelecom.gs.engine.kms.sdk.vo.prompt.PromptRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年02月10日
 */
@Service
@Slf4j
public class GsPromptRpcServiceImpl implements GsPromptRpcService {

    @Resource
    @Qualifier("ssePoolExecutor")
    private ExecutorService ssePoolExecutor;

    @Resource
    private KsGlobalConfig  ksGlobalConfig;


    @Override
    public void prompt(SseEmitter emitter, PromptRequest request) {
        ssePoolExecutor.execute(TtlRunnable.get(() -> {
            try {
                CountDownLatch latch = new CountDownLatch(1);
                GsSseProxyWatcher watcher = new GsSseProxyWatcher(latch);
                watcher.setSseEmitter(emitter);
                watcher.request(request);
                latch.await();
            } catch (Exception e) {
                log.error("sse请求异常", e);
            } finally {
                emitter.complete();
            }
        }));

    }

    @Override
    public String promptSync(PromptRequest request) {
        try {
            CountDownLatch latch = new CountDownLatch(1);
            GsSseSyncWatcher watcher = new GsSseSyncWatcher(latch);
            watcher.request(request);
            latch.await();
            return watcher.getFenceStrBuilder().toString();
        } catch (Exception e) {
            log.error("sse请求异常", e);
            BizAssert.throwBizException(e, "C0005", ArrayUtils.EMPTY_OBJECT_ARRAY, "调用gs-engine生成大纲异常");
        }
        return StringUtils.EMPTY;
    }

    @Override
    public SseEmitter modelRequest(PromptRequest param) {
        return null;
    }


    //    @Override
    public SseEmitter errorResponse(String message) throws IOException {
        String text = StringUtils.defaultIfBlank(message, ksGlobalConfig.getRag().getResponseText().get(ResponseType.ERROR));
        KsReportMessage ksReportMessage = KsReportMessage.builder().eventType(SseEventType.error)
                .text(text).build();
        return empty(JsonUtils.toJsonString(ksReportMessage));
    }

    /**
     * 直接生成文本回复
     *
     * @param text 回复的文本
     */
    public SseEmitter empty(String text) throws IOException {
        SseEmitterUTF8 sseEmitter = new SseEmitterUTF8(30 * 60 * 1000L);
        sseEmitter.send(text);
        sseEmitter.complete();
        return sseEmitter;
    }
}
