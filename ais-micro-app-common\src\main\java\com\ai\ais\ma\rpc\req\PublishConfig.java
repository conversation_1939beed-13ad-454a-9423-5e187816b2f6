package com.ai.ais.ma.rpc.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PublishConfig {
    /**
     * 发布开关
     */
    private Boolean publishSwitch;
    /**
     * 审核开关
     */
    private Boolean auditSwitch;
    /**
     * 审核编码流程编码
     */
    private String processCode;
    /**
     * 导读开关
     */
    private Boolean introductionSwitch;

}
