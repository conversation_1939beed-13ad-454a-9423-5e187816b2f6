package com.ai.ais.ma.base.infra;

import org.springframework.beans.BeanUtils;
import org.springframework.cglib.core.ReflectUtils;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * BaseConvert
 *
 * <AUTHOR>
 * @date 2023-04-08 13:08
 */
public interface BaseConvertor<DTO, PO> {

    /**
     * @param dto
     * @return
     */
    default PO convertToPo(DTO dto) {
        if (null == dto) {
            return null;
        }
        PO po = (PO) ReflectUtils.newInstance(getPOClass());
        BeanUtils.copyProperties(dto, po);
        return po;
    }

    default DTO convertToDto(PO po) {
        if (null == po) {
            return null;
        }
        DTO dto = (DTO) ReflectUtils.newInstance(getDTOClass());
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    default List<PO> convertToPo(Collection<DTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return Collections.EMPTY_LIST;
        }
        return dtoList.stream().map(dto -> convertToPo(dto)).collect(Collectors.toList());
    }

    default List<DTO> convertToDto(Collection<PO> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.EMPTY_LIST;
        }
        return poList.stream().map(this::convertToDto).collect(Collectors.toList());
    }

    Class<DTO> getDTOClass();

    Class<PO> getPOClass();

}
