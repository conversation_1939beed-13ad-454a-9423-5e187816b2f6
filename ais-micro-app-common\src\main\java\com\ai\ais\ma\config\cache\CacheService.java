package com.ai.ais.ma.config.cache;

/**
 * 一些缓存的封装操作
 *
 * <AUTHOR>
 * @date 2023-04-14 15:17
 */
public interface CacheService {

    void put(String cacheName, String cacheKey, Object cacheValue);

    <T> T get(String cacheName, String cacheKey, Class<T> type);

    void evict(String cacheName, String cacheKey);

    void evictIfPresent(String cacheName, String cacheKey);

    void clear(String cacheName);

    void invalidate(String cacheName);

}
