package ${package.Parent}.convert.dto;


import ${package.Parent}.infra.po.${entity}PO;
import ${package.Parent}.model.dto.${entity}DTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ${entity}DtoConverter{

${entity}DtoConverter INSTANCE = Mappers.getMapper(${entity}DtoConverter.class);

${entity}PO convert(${entity}DTO param);

${entity}DTO convert(${entity}PO param);
}


