package com.ai.ais.ma.base.service.impl;

import com.ai.ais.ma.base.converter.BaseVoConverter;
import com.ai.ais.ma.base.dto.BaseCodeDTO;
import com.ai.ais.ma.base.infra.BaseCodeEntity;
import com.ai.ais.ma.base.infra.repository.BaseRepository;
import com.ai.ais.ma.base.model.CodeParam;
import com.ai.ais.ma.base.service.BaseAppByCodeService;
import com.ai.ais.ma.common.utils.IPageUtils;
import com.ai.ais.ma.common.utils.IdGenerator;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.PageParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月03日
 */
public abstract class BaseAppServiceByCodeImpl<Repository extends BaseRepository<DTO, PO>,
        QUERY extends PageParam,
        COV extends BaseVoConverter<DTO, VO, C, U>,
        PO extends BaseCodeEntity,
        DTO extends BaseCodeDTO,
        VO, C, U>
        implements BaseAppByCodeService<Repository, QUERY, COV, PO, DTO, VO, C, U> {

    @Autowired
    protected Repository repository;

    protected abstract BaseVoConverter<DTO, VO, C, U> converter();


    /**
     * 根据code查询唯一一条数据
     *
     * @param code
     * @return
     */
    @Override
    public VO get(String code) {
        LambdaQueryWrapper<PO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PO::getCode, code);
        DTO dto = repository.selectOne(wrapper);
        if (dto == null) {
            return null;
        }
        return converter().convertVO(dto);
    }

    @Override
    public Page<VO> pageQuery(QUERY query) {
        IPage<DTO> page = new PageDTO<>(query.getPageNum(), query.getPageSize());
        Wrapper<PO> queryWrapper = pageQueryWrapper(query);
        page = repository.page(page, queryWrapper);
        return IPageUtils.convert(page, dto -> converter().convertVO(dto));
    }

    /**
     * 构造分页查询条件, 有额外条件覆盖该方法
     *
     * @param query
     * @return
     */
    protected Wrapper<PO> pageQueryWrapper(QUERY query) {
        return Wrappers.emptyWrapper();
    }

    @Override
    public VO create(C createParam) {
        DTO dto = converter().convertCreate(createParam);
        dto.setCode(IdGenerator.getId(StringUtils.EMPTY));
        repository.save(dto);
        return converter().convertVO(dto);
    }

    @Override
    public boolean update(String code, U updateParam) {
        DTO dto = converter().convertUpdate(updateParam);
        dto.setId(null);
        return repository.updateOne(dto, Wrappers.<PO>lambdaUpdate().eq(PO::getCode, code));
    }

    /**
     * 根据code删除
     *
     * @param codes
     * @return
     */
    @Override
    public boolean delete(CodeParam codes) {
        return repository.remove(Wrappers.<PO>lambdaUpdate().in(PO::getCode, codes.getCodes()));
    }

}
