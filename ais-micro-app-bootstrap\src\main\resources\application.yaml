spring:
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  profiles:
    active:
      - ais-ma
      - gs-common
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
    async:
      request-timeout: 300000 #5min
  messages:
    basename: i18n/message
    cache-duration: 3600
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    url: jdbc:mysql://${app.db.ma.host}:${app.db.ma.port}/${app.db.ma.dbname}?useUnicode=true&characterEncoding=utf8&useSSL=false&allowMultiQueries=true
    username: ${app.db.ma.username}
    password: ${app.db.ma.password}
    hikari:
      connection-timeout: 2000
      minimum-idle: 5
      maximum-pool-size: 15
      auto-commit: true
  aop:
    proxy-target-class: true
  servlet:
    multipart:
      max-file-size: 200MB
      max-request-size: 10GB
  flyway:
    enabled: true # 是否启用flyway
    baseline-on-migrate: true  # 空库时候自动执行建库脚本
    placeholderReplacement: false

server:
  port: 8066
  servlet:
    context-path: /ais/ma
    encoding:
      charset: UTF-8

mybatis-plus:
  mapperLocations: classpath*:/mybatis/**/*.xml
  configuration:
    mapUnderscoreToCamelCase: true
    cacheEnabled: true #默认开启二级缓存
    localCacheScope: STATEMENT # 关闭一级缓存
  global-config:
    banner: false
    db-config:
      logic-delete-field: yn # 全局逻辑删除的实体字段名
      logic-delete-value: id
      logic-not-delete-value: 0
