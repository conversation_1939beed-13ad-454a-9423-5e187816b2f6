package com.ai.ais.ma.base.infra;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 无租户的entity
 * @date 2024年03月28日
 */
@Getter
@Setter
public class RootEntity implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    protected Long id;

    /**
     * 删除标记,1:删除,0:未删除
     */
    @TableField("yn")
    @TableLogic(value = "0", delval = "id")
    protected Long yn;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    protected String createId;

    /**
     * 创建用户名
     */
    @TableField(value = "create_name", fill = FieldFill.INSERT)
    protected String createName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    protected LocalDateTime createTime;

    /**
     * 最后修改用户ID
     */
    @TableField(value = "update_id", fill = FieldFill.INSERT)
    protected String updateId;

    /**
     * 最后修改用户名
     */
    @TableField(value = "update_name", fill = FieldFill.INSERT)
    protected String updateName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT)
    protected LocalDateTime updateTime;
}
