package com.ai.ais.ma.common.utils;

import com.ai.ais.ma.common.context.RequestInfo;
import com.ai.ais.ma.config.property.KsGlobalConfig;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelelcom.gs.engine.sdk.common.constants.HeaderKeys;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月22日
 */
public class InterceptorUtils {

    /**
     * 拦截器输出错误信息说明
     *
     * @param response
     * @param code
     * @param message
     * @throws IOException
     */
    public static boolean writeError(HttpServletResponse response, String code, String message) throws IOException {
        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        response.setContentType("application/json; charset=UTF-8");
        response.getWriter().write(JsonUtils.toJsonString(Result.failed(code, message, message)));
        return false;
    }

    public static void setAppCode(HttpServletRequest request, RequestInfo requestInfo) {
        requestInfo.setAppCode(requestInfo.getTenantId());
    }

    public static void setSourceType(KsGlobalConfig ksGlobalConfig, HttpServletRequest request, RequestInfo requestInfo) {
        String referer = request.getHeader(HttpHeaders.REFERER);
        if (StringUtils.isNotBlank(referer)) {
            Map<AppSourceType, String> sourceUrlType = ksGlobalConfig.getSystem().getSourceUrlType();
            if (MapUtils.isNotEmpty(sourceUrlType)) {
                for (Map.Entry<AppSourceType, String> entry : sourceUrlType.entrySet()) {
                    AppSourceType sourceType = entry.getKey();
                    String sourceUrl = entry.getValue();
                    if (StringUtils.contains(referer, sourceUrl)) {
                        requestInfo.setAppSourceType(sourceType);
                        return;
                    }
                }
            }
        }
    }

    public static void setSourceTypeByRpc(HttpServletRequest request, RequestInfo requestInfo) {
        String appSource = request.getHeader(HeaderKeys.APP_SOURCE);
        if (StringUtils.isNotBlank(appSource)) {
            requestInfo.setAppSourceType(AppSourceType.valueOf(appSource));
        }
    }
}
