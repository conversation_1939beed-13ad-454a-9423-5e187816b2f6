package com.ai.ais.ma.rpc.service;

import com.chinatelecom.gs.engine.kms.sdk.vo.prompt.PromptRequest;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年02月10日
 */
public interface GsPromptRpcService {

    /**
     * gs-engine prompt的转发调用
     *
     * @param emitter
     * @param request
     */
    void prompt(SseEmitter emitter, PromptRequest request);

    /**
     * 同步调用流式接口获取结果
     *
     * @param request
     */
    String promptSync(PromptRequest request);

    SseEmitter modelRequest(PromptRequest param);
}
