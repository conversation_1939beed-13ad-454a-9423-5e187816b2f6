package com.ai.ais.ma.common.context;

import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月03日
 */
@Getter
@Setter
public class RequestInfo {
    private String userId;
    private String userName;
    private String tenantId;
    private String appCode;
    private List<TeamInfo> team;
    /**
     * 是否为租户用户，true为租户用户，false为非租户用户(用于mybatis中sql插件过滤条件)
     */
    private Boolean tenant;
    /**
     * 执行操作的应用类型,可能为空，通过Referer请求头进行判断
     */
    private AppSourceType appSourceType;
}
