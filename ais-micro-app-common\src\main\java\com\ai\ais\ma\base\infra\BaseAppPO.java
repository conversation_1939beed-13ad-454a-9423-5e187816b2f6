package com.ai.ais.ma.base.infra;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BaseAppPO extends BaseCodeEntity {

    @TableField(value = "app_code", fill = FieldFill.INSERT)
    private String appCode;

}



