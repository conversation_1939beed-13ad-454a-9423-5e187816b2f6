package ${package.Parent}.repository.impl;

import ${package.Parent}.base.infra.repository.impl.BaseByCodeRepositoryImpl;
import ${package.Parent}.base.infra.repository.impl.BaseByCodeRepositoryImpl;
import ${package.Parent}.convert.dto.${entity}DtoConverter;
import ${package.Parent}.infra.mapper.${entity}Mapper;
import ${package.Parent}.infra.po.${entity}PO;
import ${package.Parent}.model.dto.${entity}DTO;
import ${package.Parent}.repository.${entity}Repository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
* <p>
    * ${table.comment!} Repository接口实现
    * </p>
*
* <AUTHOR>
* @since ${date}
*/


@Service
@Slf4j
public class ${entity}RepositoryImpl extends BaseByCodeRepositoryImpl< ${entity}Mapper, ${entity}PO, ${entity}DTO> implements ${entity}Repository {

@Override
public ${entity}DTO convertToDto(${entity}PO source) {
return ${entity}DtoConverter.INSTANCE.convert(source);
}

@Override
public ${entity}PO convertToPo(${entity}DTO source) {
return ${entity}DtoConverter.INSTANCE.convert(source);
}


}

