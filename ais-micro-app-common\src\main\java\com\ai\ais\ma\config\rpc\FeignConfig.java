package com.ai.ais.ma.config.rpc;

import com.ai.ais.ma.common.context.RequestContext;
import com.chinatelelcom.gs.engine.sdk.common.constants.HeaderKeys;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import feign.RequestInterceptor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * FeignConfig
 *
 * <AUTHOR>
 * @date 2023-04-20 10:54
 */
@Configuration
@EnableFeignClients(basePackages = {"com.chinatelecom.gs.engine", "com.ai.ais.ma"})
public class FeignConfig {

    @Bean
    public RequestInterceptor feignInterceptor() {
        return template -> {
            String appCode = RequestContext.getAppCode();
            if (StringUtils.isNotBlank(appCode)) {
                template.header(HeaderKeys.APP_CODE, appCode);
            }

            String userId = RequestContext.getUserId();
            if (StringUtils.isNotBlank(userId)) {
                template.header(HeaderKeys.USER_ID, userId);
            }

            String tenantId = RequestContext.getTenantId();
            if (StringUtils.isNotBlank(tenantId)) {
                template.header(HeaderKeys.TENANT_ID, tenantId);
            }

            AppSourceType appSourceType = RequestContext.getAppSourceType();
            if (appSourceType != null) {
                template.header(HeaderKeys.APP_SOURCE, appSourceType.name());
            }

        };
    }

}
