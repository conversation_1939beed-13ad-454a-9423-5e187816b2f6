package com.ai.ais.ma.common.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.PageImpl;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * IPageUtils
 *
 * <AUTHOR>
 * @date 2023-04-10 14:17
 */
public class IPageUtils {

    /**
     * 转换Page对象
     *
     * @param pageParam
     * @param <T>
     * @return
     */
    public static <T, R> Page<R> convert(IPage<T> pageParam, Function<T, R> function) {
        PageImpl<R> page = new PageImpl<>();
        page.setCurrent(pageParam.getCurrent());
        page.setTotal(pageParam.getTotal());
        page.setSize(pageParam.getSize());
        page.setPages(pageParam.getPages());
        List<T> record = pageParam.getRecords();
        if (CollectionUtils.isNotEmpty(record)) {
            List<R> convertList = record.stream()
                    .map(function)
                    .collect(Collectors.toList());
            page.setRecords(convertList);
        }
        return page;
    }

    public static <T> PageImpl<T> convert(IPage<T> pageParam) {
        PageImpl<T> page = new PageImpl<>();
        page.setCurrent(pageParam.getCurrent());
        page.setTotal(pageParam.getTotal());
        page.setSize(pageParam.getSize());
        page.setPages(pageParam.getPages());
        page.setRecords(pageParam.getRecords());
        return page;
    }

    /**
     * 转换Page对象数据
     *
     * @param pageParam
     * @param function
     * @param <T>
     * @param <R>
     * @return
     */
    public static <T, R> Page<R> convert(Page<T> pageParam, Function<T, R> function) {
        PageImpl<R> page = new PageImpl<>();
        page.setCurrent(pageParam.getCurrent());
        page.setTotal(pageParam.getTotal());
        page.setSize(pageParam.getSize());
        page.setPages(pageParam.getPages());
        List<T> record = pageParam.getRecords();
        if (CollectionUtils.isNotEmpty(record)) {
            List<R> convertList = record.stream()
                    .map(function)
                    .collect(Collectors.toList());
            page.setRecords(convertList);
        }
        return page;
    }

    public static <T, R> Page<R> convertBatch(Page<T> pageParam, Function<List<T>, List<R>> function) {
        PageImpl<R> page = new PageImpl<>();
        page.setCurrent(pageParam.getCurrent());
        page.setTotal(pageParam.getTotal());
        page.setSize(pageParam.getSize());
        page.setPages(pageParam.getPages());
        List<T> record = pageParam.getRecords();
        if (CollectionUtils.isNotEmpty(record)) {
            List<R> convertList = function.apply(record);
            page.setRecords(convertList);
        }
        return page;
    }

    /**
     * @param pageParam 分页对象
     * @param function  批量转换函数
     * @return com.chinatelecom.ai.base.common.sdk.Page<R>
     * @Description 批量转换iPage对象
     * <AUTHOR>
     * @Date 2023/8/30
     */
    public static <T, R> Page<R> convertBatch(IPage<T> pageParam, Function<List<T>, List<R>> function) {
        PageImpl<R> page = new PageImpl<>();
        page.setCurrent(pageParam.getCurrent());
        page.setTotal(pageParam.getTotal());
        page.setSize(pageParam.getSize());
        page.setPages(pageParam.getPages());
        List<T> record = pageParam.getRecords();
        if (CollectionUtils.isNotEmpty(record)) {
            List<R> convertList = function.apply(record);
            page.setRecords(convertList);
        }
        return page;
    }
}
