package com.ai.ais.ma.config.cache.aspect.annotation;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * 分布式锁注解
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Lock {
    /**
     * 锁的key
     */
    String key();

    /**
     * 获取锁的最大尝试时间(单位 {@code unit})
     * 大于0会尝试等待的时间,超过时间后会放弃等待获取锁失败
     * 等于零如果获取不到锁会立即失败
     * 小于0会无限制阻塞等待,直到获取到锁
     */
    long waitTime() default 0;

    /**
     * 加锁的时间(单位 {@code unit})，超过这个时间后锁便自动解锁；
     * 如果leaseTime为-1，则保持锁定直到显式解锁
     * 如果大于0,则超时后自动释放(仅对redis锁有效)
     */
    long leaseTime() default -1;

    /**
     * 参数的时间单位
     */
    TimeUnit unit() default TimeUnit.SECONDS;

    /**
     * 是否公平锁
     */
    boolean isFair() default false;

    /**
     * 获取锁失败返回前端的提示信息
     *
     * @return
     */
    String message() default "业务正在处理中,请勿重复提交";
}
