package com.ai.ais.ma.common.context;

import com.ai.ais.ma.common.utils.BizAssert;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Getter
public class RequestContext {

    private static final TransmittableThreadLocal<RequestInfo> REQUEST_INFO = new TransmittableThreadLocal<>();

    public static void set(RequestInfo info) {
        REQUEST_INFO.set(info);
    }

    public static RequestInfo get() {
        return REQUEST_INFO.get();
    }

    public static RequestInfo getAndCheck() {
        RequestInfo info = get();
        BizAssert.notNull(info, "A0011", "请求上下文参数信息不能为空");
        return info;
    }


    public static void remove() {
        REQUEST_INFO.remove();
    }

    public static String getUserId() {
        String userId = getAndCheck().getUserId();
        return userId;
    }

    public static String getTenantId() {
        String tenantId = getAndCheck().getTenantId();
        return tenantId;
    }

    public static AppSourceType getAppSourceType() {
        return getAndCheck().getAppSourceType();
    }

    public static String getAppCode() {
        return getAndCheck().getAppCode();
    }

    public static void setAppCode(String appCode) {
        getAndCheck().setAppCode(appCode);
    }

    public static List<TeamInfo> getTeam() {
        return getAndCheck().getTeam();
    }

    public static List<String> getTeamCodes() {
        List<TeamInfo> teams = getCheckedTeam();
        if (CollectionUtils.isNotEmpty(teams)) {
            return teams.stream().map(TeamInfo::getTeamCode).collect(Collectors.toList());
        } else {
            return Lists.newArrayList();
        }
    }

    public static String getCheckedTenantId() {
        String tenantId = getTenantId();
        Preconditions.checkNotNull(tenantId, "租户不存在");
        return tenantId;
    }

    public static String getCheckedUserId() {
        String userId = getAndCheck().getUserId();
        Preconditions.checkNotNull(userId, "用户ID不存在");
        return userId;
    }

    public static String getCheckedAppCode() {
        String appCode = getAppCode();
        Preconditions.checkNotNull(appCode, "appCode不存在");
        return appCode;
    }

    public static List<TeamInfo> getCheckedTeam() {
        List<TeamInfo> teams = getTeam();
        if (CollectionUtils.isEmpty(teams)) {
            return Lists.newArrayList();
        }
        return teams;
    }

}
