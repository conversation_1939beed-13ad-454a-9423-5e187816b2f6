[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.185 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.185 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.194 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.194 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.204 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.204 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.213 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.213 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.222 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.222 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.234 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.235 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.247 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.247 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.257 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.257 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.266 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.266 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.276 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.276 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.284 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.284 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.292 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.292 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.304 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.304 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.313 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.314 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.323 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.323 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.331 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.331 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:56:32.972 ERROR 57064 trace_id=[][] [http-nio-8066-exec-5] c.a.a.m.c.mvc.GlobalExceptionHandler      IO异常

java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method) ~[na:1.8.0_452]
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51) ~[na:1.8.0_452]
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93) ~[na:1.8.0_452]
	at sun.nio.ch.IOUtil.write(IOUtil.java:65) ~[na:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:470) ~[na:1.8.0_452]
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:140) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1430) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:775) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:739) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:723) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:574) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:156) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:216) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1273) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:407) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.Response.action(Response.java:207) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:304) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:270) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:133) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.springframework.session.web.http.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:499) ~[spring-session-core-2.7.4.jar:2.7.4]
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.flush(StandardServletAsyncWebRequest.java:389) ~[spring-web-5.3.37.jar:5.3.37]
	at sun.nio.cs.StreamEncoder.implFlush(StreamEncoder.java:297) ~[na:1.8.0_452]
	at sun.nio.cs.StreamEncoder.flush(StreamEncoder.java:141) ~[na:1.8.0_452]
	at java.io.OutputStreamWriter.flush(OutputStreamWriter.java:229) ~[na:1.8.0_452]
	at org.springframework.util.StreamUtils.copy(StreamUtils.java:148) ~[spring-core-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.StringHttpMessageConverter.writeInternal(StringHttpMessageConverter.java:125) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.StringHttpMessageConverter.writeInternal(StringHttpMessageConverter.java:44) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.AbstractHttpMessageConverter.write(AbstractHttpMessageConverter.java:227) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler$HttpMessageConvertingHandler.sendInternal(ResponseBodyEmitterReturnValueHandler.java:212) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler$HttpMessageConvertingHandler.send(ResponseBodyEmitterReturnValueHandler.java:205) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter.sendInternal(ResponseBodyEmitter.java:204) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter.send(ResponseBodyEmitter.java:198) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.SseEmitter.send(SseEmitter.java:127) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at com.ai.ais.ma.utils.sse.GsOutlineStreamWatcher.onEvent(GsOutlineStreamWatcher.java:78) ~[classes/:na]
	at okhttp3.internal.sse.RealEventSource.onEvent(RealEventSource.kt:101) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.ServerSentEventReader.completeEvent(ServerSentEventReader.kt:108) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.ServerSentEventReader.processNextEvent(ServerSentEventReader.kt:52) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.RealEventSource.processResponse(RealEventSource.kt:75) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.RealEventSource.onResponse(RealEventSource.kt:46) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519) ~[okhttp-4.9.3.jar:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:60) ~[transmittable-thread-local-2.14.1.jar:na]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_452]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_452]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_452]

[ais-ks:172.25.112.1:8066] 2025-06-18 16:56:32.985 ERROR 57064 trace_id=[][] [http-nio-8066-exec-5] o.a.c.c.C.[.[.[.[dispatcherServlet]       Servlet.service() for servlet [dispatcherServlet] threw exception

java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method) ~[na:1.8.0_452]
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51) ~[na:1.8.0_452]
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93) ~[na:1.8.0_452]
	at sun.nio.ch.IOUtil.write(IOUtil.java:65) ~[na:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:470) ~[na:1.8.0_452]
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:140) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1430) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:775) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:739) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:723) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:574) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:156) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:216) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1273) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:407) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.Response.action(Response.java:207) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:304) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:270) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:133) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.springframework.session.web.http.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:499) ~[spring-session-core-2.7.4.jar:2.7.4]
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.flush(StandardServletAsyncWebRequest.java:389) ~[spring-web-5.3.37.jar:5.3.37]
	at sun.nio.cs.StreamEncoder.implFlush(StreamEncoder.java:297) ~[na:1.8.0_452]
	at sun.nio.cs.StreamEncoder.flush(StreamEncoder.java:141) ~[na:1.8.0_452]
	at java.io.OutputStreamWriter.flush(OutputStreamWriter.java:229) ~[na:1.8.0_452]
	at org.springframework.util.StreamUtils.copy(StreamUtils.java:148) ~[spring-core-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.StringHttpMessageConverter.writeInternal(StringHttpMessageConverter.java:125) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.StringHttpMessageConverter.writeInternal(StringHttpMessageConverter.java:44) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.AbstractHttpMessageConverter.write(AbstractHttpMessageConverter.java:227) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler$HttpMessageConvertingHandler.sendInternal(ResponseBodyEmitterReturnValueHandler.java:212) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler$HttpMessageConvertingHandler.send(ResponseBodyEmitterReturnValueHandler.java:205) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter.sendInternal(ResponseBodyEmitter.java:204) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter.send(ResponseBodyEmitter.java:198) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.SseEmitter.send(SseEmitter.java:127) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at com.ai.ais.ma.utils.sse.GsOutlineStreamWatcher.onEvent(GsOutlineStreamWatcher.java:78) ~[classes/:na]
	at okhttp3.internal.sse.RealEventSource.onEvent(RealEventSource.kt:101) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.ServerSentEventReader.completeEvent(ServerSentEventReader.kt:108) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.ServerSentEventReader.processNextEvent(ServerSentEventReader.kt:52) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.RealEventSource.processResponse(RealEventSource.kt:75) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.RealEventSource.onResponse(RealEventSource.kt:46) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519) ~[okhttp-4.9.3.jar:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:60) ~[transmittable-thread-local-2.14.1.jar:na]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_452]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_452]
	at java.lang.Thread.run(Thread.java:750) [na:1.8.0_452]

[ais-ks:172.25.112.1:8066] 2025-06-18 16:56:32.985 ERROR 57064 trace_id=[][] [http-nio-8066-exec-5] o.a.c.c.C.[.[.[.[dispatcherServlet]       Servlet.service() for servlet [dispatcherServlet] in context with path [/ais/ma] threw exception

java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method) ~[na:1.8.0_452]
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51) ~[na:1.8.0_452]
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93) ~[na:1.8.0_452]
	at sun.nio.ch.IOUtil.write(IOUtil.java:65) ~[na:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:470) ~[na:1.8.0_452]
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:140) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1430) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:775) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:739) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:723) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:574) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:156) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:216) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1273) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:407) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.Response.action(Response.java:207) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:304) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:270) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:133) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.springframework.session.web.http.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:499) ~[spring-session-core-2.7.4.jar:2.7.4]
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.flush(StandardServletAsyncWebRequest.java:389) ~[spring-web-5.3.37.jar:5.3.37]
	at sun.nio.cs.StreamEncoder.implFlush(StreamEncoder.java:297) ~[na:1.8.0_452]
	at sun.nio.cs.StreamEncoder.flush(StreamEncoder.java:141) ~[na:1.8.0_452]
	at java.io.OutputStreamWriter.flush(OutputStreamWriter.java:229) ~[na:1.8.0_452]
	at org.springframework.util.StreamUtils.copy(StreamUtils.java:148) ~[spring-core-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.StringHttpMessageConverter.writeInternal(StringHttpMessageConverter.java:125) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.StringHttpMessageConverter.writeInternal(StringHttpMessageConverter.java:44) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.AbstractHttpMessageConverter.write(AbstractHttpMessageConverter.java:227) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler$HttpMessageConvertingHandler.sendInternal(ResponseBodyEmitterReturnValueHandler.java:212) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler$HttpMessageConvertingHandler.send(ResponseBodyEmitterReturnValueHandler.java:205) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter.sendInternal(ResponseBodyEmitter.java:204) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter.send(ResponseBodyEmitter.java:198) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.SseEmitter.send(SseEmitter.java:127) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at com.ai.ais.ma.utils.sse.GsOutlineStreamWatcher.onEvent(GsOutlineStreamWatcher.java:78) ~[classes/:na]
	at okhttp3.internal.sse.RealEventSource.onEvent(RealEventSource.kt:101) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.ServerSentEventReader.completeEvent(ServerSentEventReader.kt:108) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.ServerSentEventReader.processNextEvent(ServerSentEventReader.kt:52) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.RealEventSource.processResponse(RealEventSource.kt:75) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.RealEventSource.onResponse(RealEventSource.kt:46) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519) ~[okhttp-4.9.3.jar:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:60) ~[transmittable-thread-local-2.14.1.jar:na]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_452]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_452]
	at java.lang.Thread.run(Thread.java:750) [na:1.8.0_452]

