[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.185 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.185 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.194 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.194 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.204 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.204 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.213 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.213 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.222 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.222 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.234 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.235 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.247 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.247 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.257 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.257 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.266 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.266 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.276 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.276 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.284 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.284 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.292 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.292 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.304 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.304 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.313 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.314 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.323 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.323 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.331 ERROR 57064 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 16:51:42.331 ERROR 57064 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 16:56:32.972 ERROR 57064 trace_id=[][] [http-nio-8066-exec-5] c.a.a.m.c.mvc.GlobalExceptionHandler      IO异常

java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method) ~[na:1.8.0_452]
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51) ~[na:1.8.0_452]
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93) ~[na:1.8.0_452]
	at sun.nio.ch.IOUtil.write(IOUtil.java:65) ~[na:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:470) ~[na:1.8.0_452]
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:140) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1430) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:775) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:739) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:723) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:574) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:156) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:216) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1273) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:407) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.Response.action(Response.java:207) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:304) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:270) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:133) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.springframework.session.web.http.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:499) ~[spring-session-core-2.7.4.jar:2.7.4]
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.flush(StandardServletAsyncWebRequest.java:389) ~[spring-web-5.3.37.jar:5.3.37]
	at sun.nio.cs.StreamEncoder.implFlush(StreamEncoder.java:297) ~[na:1.8.0_452]
	at sun.nio.cs.StreamEncoder.flush(StreamEncoder.java:141) ~[na:1.8.0_452]
	at java.io.OutputStreamWriter.flush(OutputStreamWriter.java:229) ~[na:1.8.0_452]
	at org.springframework.util.StreamUtils.copy(StreamUtils.java:148) ~[spring-core-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.StringHttpMessageConverter.writeInternal(StringHttpMessageConverter.java:125) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.StringHttpMessageConverter.writeInternal(StringHttpMessageConverter.java:44) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.AbstractHttpMessageConverter.write(AbstractHttpMessageConverter.java:227) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler$HttpMessageConvertingHandler.sendInternal(ResponseBodyEmitterReturnValueHandler.java:212) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler$HttpMessageConvertingHandler.send(ResponseBodyEmitterReturnValueHandler.java:205) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter.sendInternal(ResponseBodyEmitter.java:204) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter.send(ResponseBodyEmitter.java:198) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.SseEmitter.send(SseEmitter.java:127) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at com.ai.ais.ma.utils.sse.GsOutlineStreamWatcher.onEvent(GsOutlineStreamWatcher.java:78) ~[classes/:na]
	at okhttp3.internal.sse.RealEventSource.onEvent(RealEventSource.kt:101) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.ServerSentEventReader.completeEvent(ServerSentEventReader.kt:108) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.ServerSentEventReader.processNextEvent(ServerSentEventReader.kt:52) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.RealEventSource.processResponse(RealEventSource.kt:75) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.RealEventSource.onResponse(RealEventSource.kt:46) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519) ~[okhttp-4.9.3.jar:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:60) ~[transmittable-thread-local-2.14.1.jar:na]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_452]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_452]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_452]

[ais-ks:172.25.112.1:8066] 2025-06-18 16:56:32.985 ERROR 57064 trace_id=[][] [http-nio-8066-exec-5] o.a.c.c.C.[.[.[.[dispatcherServlet]       Servlet.service() for servlet [dispatcherServlet] threw exception

java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method) ~[na:1.8.0_452]
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51) ~[na:1.8.0_452]
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93) ~[na:1.8.0_452]
	at sun.nio.ch.IOUtil.write(IOUtil.java:65) ~[na:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:470) ~[na:1.8.0_452]
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:140) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1430) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:775) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:739) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:723) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:574) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:156) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:216) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1273) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:407) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.Response.action(Response.java:207) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:304) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:270) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:133) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.springframework.session.web.http.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:499) ~[spring-session-core-2.7.4.jar:2.7.4]
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.flush(StandardServletAsyncWebRequest.java:389) ~[spring-web-5.3.37.jar:5.3.37]
	at sun.nio.cs.StreamEncoder.implFlush(StreamEncoder.java:297) ~[na:1.8.0_452]
	at sun.nio.cs.StreamEncoder.flush(StreamEncoder.java:141) ~[na:1.8.0_452]
	at java.io.OutputStreamWriter.flush(OutputStreamWriter.java:229) ~[na:1.8.0_452]
	at org.springframework.util.StreamUtils.copy(StreamUtils.java:148) ~[spring-core-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.StringHttpMessageConverter.writeInternal(StringHttpMessageConverter.java:125) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.StringHttpMessageConverter.writeInternal(StringHttpMessageConverter.java:44) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.AbstractHttpMessageConverter.write(AbstractHttpMessageConverter.java:227) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler$HttpMessageConvertingHandler.sendInternal(ResponseBodyEmitterReturnValueHandler.java:212) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler$HttpMessageConvertingHandler.send(ResponseBodyEmitterReturnValueHandler.java:205) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter.sendInternal(ResponseBodyEmitter.java:204) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter.send(ResponseBodyEmitter.java:198) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.SseEmitter.send(SseEmitter.java:127) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at com.ai.ais.ma.utils.sse.GsOutlineStreamWatcher.onEvent(GsOutlineStreamWatcher.java:78) ~[classes/:na]
	at okhttp3.internal.sse.RealEventSource.onEvent(RealEventSource.kt:101) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.ServerSentEventReader.completeEvent(ServerSentEventReader.kt:108) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.ServerSentEventReader.processNextEvent(ServerSentEventReader.kt:52) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.RealEventSource.processResponse(RealEventSource.kt:75) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.RealEventSource.onResponse(RealEventSource.kt:46) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519) ~[okhttp-4.9.3.jar:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:60) ~[transmittable-thread-local-2.14.1.jar:na]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_452]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_452]
	at java.lang.Thread.run(Thread.java:750) [na:1.8.0_452]

[ais-ks:172.25.112.1:8066] 2025-06-18 16:56:32.985 ERROR 57064 trace_id=[][] [http-nio-8066-exec-5] o.a.c.c.C.[.[.[.[dispatcherServlet]       Servlet.service() for servlet [dispatcherServlet] in context with path [/ais/ma] threw exception

java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.write0(Native Method) ~[na:1.8.0_452]
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:51) ~[na:1.8.0_452]
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93) ~[na:1.8.0_452]
	at sun.nio.ch.IOUtil.write(IOUtil.java:65) ~[na:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:470) ~[na:1.8.0_452]
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:140) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1430) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:775) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:739) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:723) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:574) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:156) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:216) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1273) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:407) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.coyote.Response.action(Response.java:207) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:304) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:270) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:133) ~[tomcat-embed-core-9.0.100.jar:9.0.100]
	at org.springframework.session.web.http.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:499) ~[spring-session-core-2.7.4.jar:2.7.4]
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.flush(StandardServletAsyncWebRequest.java:389) ~[spring-web-5.3.37.jar:5.3.37]
	at sun.nio.cs.StreamEncoder.implFlush(StreamEncoder.java:297) ~[na:1.8.0_452]
	at sun.nio.cs.StreamEncoder.flush(StreamEncoder.java:141) ~[na:1.8.0_452]
	at java.io.OutputStreamWriter.flush(OutputStreamWriter.java:229) ~[na:1.8.0_452]
	at org.springframework.util.StreamUtils.copy(StreamUtils.java:148) ~[spring-core-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.StringHttpMessageConverter.writeInternal(StringHttpMessageConverter.java:125) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.StringHttpMessageConverter.writeInternal(StringHttpMessageConverter.java:44) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.http.converter.AbstractHttpMessageConverter.write(AbstractHttpMessageConverter.java:227) ~[spring-web-5.3.37.jar:5.3.37]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler$HttpMessageConvertingHandler.sendInternal(ResponseBodyEmitterReturnValueHandler.java:212) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitterReturnValueHandler$HttpMessageConvertingHandler.send(ResponseBodyEmitterReturnValueHandler.java:205) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter.sendInternal(ResponseBodyEmitter.java:204) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter.send(ResponseBodyEmitter.java:198) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.SseEmitter.send(SseEmitter.java:127) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at com.ai.ais.ma.utils.sse.GsOutlineStreamWatcher.onEvent(GsOutlineStreamWatcher.java:78) ~[classes/:na]
	at okhttp3.internal.sse.RealEventSource.onEvent(RealEventSource.kt:101) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.ServerSentEventReader.completeEvent(ServerSentEventReader.kt:108) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.ServerSentEventReader.processNextEvent(ServerSentEventReader.kt:52) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.RealEventSource.processResponse(RealEventSource.kt:75) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.sse.RealEventSource.onResponse(RealEventSource.kt:46) ~[okhttp-sse-4.9.3.jar:na]
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519) ~[okhttp-4.9.3.jar:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:60) ~[transmittable-thread-local-2.14.1.jar:na]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_452]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_452]
	at java.lang.Thread.run(Thread.java:750) [na:1.8.0_452]

[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.146 ERROR 64824 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.147 ERROR 64824 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.155 ERROR 64824 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.155 ERROR 64824 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.162 ERROR 64824 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.162 ERROR 64824 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.170 ERROR 64824 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.170 ERROR 64824 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.177 ERROR 64824 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.177 ERROR 64824 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.184 ERROR 64824 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.184 ERROR 64824 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.192 ERROR 64824 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.192 ERROR 64824 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.199 ERROR 64824 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.199 ERROR 64824 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.207 ERROR 64824 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.207 ERROR 64824 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.214 ERROR 64824 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.214 ERROR 64824 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.220 ERROR 64824 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.220 ERROR 64824 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.228 ERROR 64824 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.228 ERROR 64824 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.234 ERROR 64824 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.234 ERROR 64824 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.243 ERROR 64824 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.243 ERROR 64824 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.250 ERROR 64824 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.250 ERROR 64824 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.256 ERROR 64824 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 17:46:46.256 ERROR 64824 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.198 ERROR 40260 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.198 ERROR 40260 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.206 ERROR 40260 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.207 ERROR 40260 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.215 ERROR 40260 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.215 ERROR 40260 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.222 ERROR 40260 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.222 ERROR 40260 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.229 ERROR 40260 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.230 ERROR 40260 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.239 ERROR 40260 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.239 ERROR 40260 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.247 ERROR 40260 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.247 ERROR 40260 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.255 ERROR 40260 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.255 ERROR 40260 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.262 ERROR 40260 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.262 ERROR 40260 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.271 ERROR 40260 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.272 ERROR 40260 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.280 ERROR 40260 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.280 ERROR 40260 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.286 ERROR 40260 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.286 ERROR 40260 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.294 ERROR 40260 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.294 ERROR 40260 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.301 ERROR 40260 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.301 ERROR 40260 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.310 ERROR 40260 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.310 ERROR 40260 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.317 ERROR 40260 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:04:50.317 ERROR 40260 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.767 ERROR 48324 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.767 ERROR 48324 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.782 ERROR 48324 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.782 ERROR 48324 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.791 ERROR 48324 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.791 ERROR 48324 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.800 ERROR 48324 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.800 ERROR 48324 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.810 ERROR 48324 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.810 ERROR 48324 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.823 ERROR 48324 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.824 ERROR 48324 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.832 ERROR 48324 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.832 ERROR 48324 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.839 ERROR 48324 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.839 ERROR 48324 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.846 ERROR 48324 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.847 ERROR 48324 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.855 ERROR 48324 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.855 ERROR 48324 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.865 ERROR 48324 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.865 ERROR 48324 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.874 ERROR 48324 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.874 ERROR 48324 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.882 ERROR 48324 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.883 ERROR 48324 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.893 ERROR 48324 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.893 ERROR 48324 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.900 ERROR 48324 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.901 ERROR 48324 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.910 ERROR 48324 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:10:46.910 ERROR 48324 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.230 ERROR 61548 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.231 ERROR 61548 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.241 ERROR 61548 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.241 ERROR 61548 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.251 ERROR 61548 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.252 ERROR 61548 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.262 ERROR 61548 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.262 ERROR 61548 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.270 ERROR 61548 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.270 ERROR 61548 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.281 ERROR 61548 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.281 ERROR 61548 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.291 ERROR 61548 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.291 ERROR 61548 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.299 ERROR 61548 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.299 ERROR 61548 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.308 ERROR 61548 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.309 ERROR 61548 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.319 ERROR 61548 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.319 ERROR 61548 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.329 ERROR 61548 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.329 ERROR 61548 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.337 ERROR 61548 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.337 ERROR 61548 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.345 ERROR 61548 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.345 ERROR 61548 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.355 ERROR 61548 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.355 ERROR 61548 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.364 ERROR 61548 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.364 ERROR 61548 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.372 ERROR 61548 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:17:14.373 ERROR 61548 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:17.959 ERROR 74572 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:17.959 ERROR 74572 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:17.968 ERROR 74572 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:17.968 ERROR 74572 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:17.975 ERROR 74572 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:17.975 ERROR 74572 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:17.983 ERROR 74572 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:17.984 ERROR 74572 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:17.991 ERROR 74572 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:17.991 ERROR 74572 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:17.998 ERROR 74572 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:17.998 ERROR 74572 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:18.008 ERROR 74572 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:18.008 ERROR 74572 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:18.016 ERROR 74572 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:18.016 ERROR 74572 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:18.023 ERROR 74572 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:18.023 ERROR 74572 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:18.031 ERROR 74572 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:18.032 ERROR 74572 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:18.039 ERROR 74572 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:18.039 ERROR 74572 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:18.046 ERROR 74572 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:18.046 ERROR 74572 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:18.053 ERROR 74572 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:18.053 ERROR 74572 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:18.061 ERROR 74572 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:18.061 ERROR 74572 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:18.068 ERROR 74572 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:18.069 ERROR 74572 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:18.076 ERROR 74572 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:22:18.076 ERROR 74572 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.540 ERROR 67368 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.540 ERROR 67368 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.550 ERROR 67368 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.550 ERROR 67368 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.558 ERROR 67368 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.558 ERROR 67368 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.566 ERROR 67368 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.567 ERROR 67368 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.575 ERROR 67368 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.576 ERROR 67368 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.583 ERROR 67368 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.583 ERROR 67368 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.592 ERROR 67368 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.592 ERROR 67368 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.600 ERROR 67368 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.600 ERROR 67368 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.609 ERROR 67368 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.609 ERROR 67368 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.616 ERROR 67368 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.616 ERROR 67368 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.624 ERROR 67368 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.624 ERROR 67368 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.633 ERROR 67368 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.633 ERROR 67368 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.642 ERROR 67368 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.642 ERROR 67368 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.650 ERROR 67368 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.650 ERROR 67368 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.657 ERROR 67368 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.657 ERROR 67368 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.665 ERROR 67368 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:23:08.665 ERROR 67368 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.032 ERROR 35152 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.032 ERROR 35152 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.041 ERROR 35152 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.041 ERROR 35152 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.049 ERROR 35152 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.049 ERROR 35152 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.059 ERROR 35152 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.059 ERROR 35152 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.068 ERROR 35152 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.068 ERROR 35152 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.078 ERROR 35152 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.078 ERROR 35152 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.086 ERROR 35152 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.086 ERROR 35152 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.095 ERROR 35152 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.095 ERROR 35152 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.105 ERROR 35152 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.105 ERROR 35152 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.115 ERROR 35152 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.115 ERROR 35152 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.123 ERROR 35152 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.123 ERROR 35152 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.131 ERROR 35152 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.132 ERROR 35152 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.142 ERROR 35152 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.142 ERROR 35152 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.154 ERROR 35152 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.154 ERROR 35152 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.169 ERROR 35152 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.170 ERROR 35152 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.177 ERROR 35152 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:45:01.177 ERROR 35152 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:10.910 ERROR 55420 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:10.910 ERROR 55420 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:10.920 ERROR 55420 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:10.921 ERROR 55420 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:10.929 ERROR 55420 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:10.930 ERROR 55420 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:10.939 ERROR 55420 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:10.939 ERROR 55420 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:10.950 ERROR 55420 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:10.950 ERROR 55420 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:10.959 ERROR 55420 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:10.960 ERROR 55420 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:10.969 ERROR 55420 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:10.970 ERROR 55420 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:10.984 ERROR 55420 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:10.984 ERROR 55420 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:10.994 ERROR 55420 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:10.994 ERROR 55420 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:11.002 ERROR 55420 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:11.002 ERROR 55420 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:11.010 ERROR 55420 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:11.010 ERROR 55420 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:11.019 ERROR 55420 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:11.019 ERROR 55420 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:11.028 ERROR 55420 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:11.028 ERROR 55420 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:11.037 ERROR 55420 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:11.037 ERROR 55420 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:11.047 ERROR 55420 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:11.047 ERROR 55420 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:11.056 ERROR 55420 trace_id=[][] [main] c.c.c.p.client.util.PermissionUtils       post exception, return null. url:http://192.168.130.249:32088/platform/permission/register
[ais-ks:172.25.112.1:8066] 2025-06-18 18:50:11.057 ERROR 55420 trace_id=[][] [main] c.a.a.ma.platform.PlatformRestApiReport   上报API失败
