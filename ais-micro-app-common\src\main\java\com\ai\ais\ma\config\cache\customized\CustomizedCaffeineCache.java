package com.ai.ais.ma.config.cache.customized;

import com.github.benmanes.caffeine.cache.Cache;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * CustomizedCaffeineCache
 *
 * <AUTHOR>
 * @date 2023-05-26 17:02
 */
public class CustomizedCaffeineCache extends Caf<PERSON>ineCache {

    private final String name;

    private final Cache<Object, Object> cache;

    private final boolean allowNullValues;

    public CustomizedCaffeineCache(String name, Cache<Object, Object> cache) {
        this(name, cache, true);
    }

    public CustomizedCaffeineCache(String name, Cache<Object, Object> cache, boolean allowNullValues) {
        super(name, cache, allowNullValues);
        this.name = name;
        this.cache = cache;
        this.allowNullValues = allowNullValues;
    }

    /**
     * 未命中数据时不强制报错
     *
     * @param key   the key with which the specified value is to be associated
     * @param value the value to be associated with the specified key
     */
    @Override
    public void put(Object key, @Nullable Object value) {
        if (value == null && !super.isAllowNullValues()) {
            return;
        }
        super.put(key, value);
    }

    /**
     * 未命中数据时不强制报错
     *
     * @param key   the key with which the specified value is to be associated
     * @param value the value to be associated with the specified key
     * @return
     */
    @Override
    @Nullable
    public ValueWrapper putIfAbsent(Object key, @Nullable final Object value) {
        if (value == null && !super.isAllowNullValues()) {
            return null;
        }
        return super.putIfAbsent(key, value);
    }


    /**
     * 增加后缀*匹配
     *
     * @param key the key whose mapping is to be removed from the cache
     */
    @Override
    public void evict(Object key) {
        if (key instanceof String) {
            String keyString = key.toString();
            String keyPrefix = keyString.substring(0, keyString.length() - 1);
            if (StringUtils.endsWith(keyString, "*")) {
                ConcurrentMap<Object, Object> map = this.cache.asMap();
                if (map != null && !map.isEmpty()) {
                    Set<Object> keys = map.keySet();
                    List<String> prefixList = keys.stream().filter(k -> StringUtils.startsWith(k.toString(), keyPrefix))
                            .map(k -> String.valueOf(k)).collect(Collectors.toList());
                    this.cache.invalidateAll(prefixList);
                }
                return;
            }
        }
        this.cache.invalidate(key);
    }

    @Override
    public boolean evictIfPresent(Object key) {
        evict(key);
        return false;
    }

}
