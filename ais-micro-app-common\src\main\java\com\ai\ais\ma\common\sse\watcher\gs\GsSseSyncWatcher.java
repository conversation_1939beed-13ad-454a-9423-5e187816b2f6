package com.ai.ais.ma.common.sse.watcher.gs;

import com.ai.ais.ma.common.sse.watcher.AbstractSseWatcher;
import com.ai.ais.ma.common.utils.JsonUtils;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.sse.EventSource;
import org.apache.commons.lang3.StringUtils;

import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年02月11日
 */
@Slf4j
public class GsSseSyncWatcher extends AbstractSseWatcher {

    @Getter
    /**
     * 存储大模型的所有输出
     */
    protected StringBuffer fenceStrBuilder = new StringBuffer();


    public GsSseSyncWatcher(CountDownLatch latch) {
        super(latch);
    }

    @Override
    public void onEvent(EventSource eventSource, String id, String type, String data) {
        try {
            log.debug("接收到消息内容：{}", data);
            if (StringUtils.isNotBlank(data)) {
                if (data.trim().startsWith("{")) {
                    try {
                        GsMsgData gsMsgData = JsonUtils.parseObject(data, GsMsgData.class);
                        if (gsMsgData != null && gsMsgData.getText() != null) {
                            fenceStrBuilder.append(gsMsgData.getText());
                        }
                    } catch (Exception e) {
                        log.error("消息内容不是合法的Json格式,消息将忽略, data:{}", data, e);
                    }
                }
            }
        } catch (Exception e) {
            clientAbort = true;
            throw new BizException(e, "AK002", "流式输出异常");
        }
    }

}
